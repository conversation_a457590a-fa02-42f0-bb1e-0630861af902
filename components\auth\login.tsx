"use client";
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Open_Sans, Inter, Roboto } from "next/font/google";
import { Button, Input, message } from "antd";
import AppleIcon from "@mui/icons-material/Apple";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  loginWithApple,
  loginWithGoogle,
  signInWithFirebase,
} from "@/src/api/auth.api";
import appConfig from "@/src/config/apps";
import { useIndexedDBContext } from "@/src/context/indexedDB.context";
import FullScreenLoader from "../ui/fullScreenLoader";
import { useSidebarContext } from "@/src/context/sidebar.context";
import { getFCMToken } from "@/src/config/firebase.config";
import {
  fetchUserSettings,
  subscribeTopic,
  updateUserSettings,
} from "@/src/api/settings.api";

const open_Sans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const inter = Inter({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Login = () => {
  const router = useRouter();
  const { syncData, isSyncing } = useIndexedDBContext();
  const { setIsTabChangeLoading } = useSidebarContext();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const [submitted, setSubmitted] = useState(false);
  const [loader, setLoader] = useState(false);
  const [showSyncLoader, setShowSyncLoader] = useState(false);
  const [fcmLoader, setFcmLoader] = useState(true);

  const [fcmToken, setFcmToken] = useState("");
  const isInitialized = useRef(false);

  useEffect(() => {
    const registerServiceWorkerAndGetToken = async () => {
      try {
        setFcmLoader(true);

        // Check if service worker is already registered
        let registration = await navigator.serviceWorker.getRegistration(
          "/firebase-messaging-sw.js"
        );

        if (!registration) {
          // Register the service worker if not already registered
          console.log("Registering new service worker...");
          registration = await navigator.serviceWorker.register(
            "/firebase-messaging-sw.js",
            { scope: "/" }
          );
          console.log("Service Worker registered:", registration);
        } else {
          console.log("Service Worker already registered:", registration);
        }

        // Wait for the service worker to be ready
        await navigator.serviceWorker.ready;
        console.log("Service Worker is ready");

        // Get the FCM token
        const token = await getFCMToken(registration);
        // message.success(`FCM Token retrieved successfully ${token}`);

        if (token) {
          console.log("FCM Token retrieved:", token);

          // Handle iOS Safari placeholder token
          if (token === "ios-safari-not-supported") {
            console.warn("Using placeholder token for iOS Safari");
            localStorage.setItem("fcmToken", token);
            setFcmToken(token);
            // Show a message to the user that notifications may not work on iOS Safari
          } else {
            localStorage.setItem("fcmToken", token);
            setFcmToken(token);
          }
        } else {
          console.error("No FCM Token available.");
          localStorage.setItem("fcmToken", "");
        }
      } catch (error) {
        console.error("Error during Service Worker or FCM setup:", error);
        // message.error(`Error during Service Worker or FCM setup: ${error}`);
        localStorage.setItem("fcmToken", "");
      } finally {
        setFcmLoader(false);
      }
    };

    if (!isInitialized.current && "serviceWorker" in navigator) {
      isInitialized.current = true;
      const fcm = localStorage.getItem("fcmToken");
      if (fcm) {
        setFcmToken(fcm);
        setFcmLoader(false);
      } else {
        registerServiceWorkerAndGetToken();
      }
    }
  }, []);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSignIn = async () => {
    try {
      setSubmitted(true); // Mark form as submitted

      if (validateEmail(email) && password.trim().length >= 8) {
        setLoader(true);
        const { userCredential, idToken } = await signInWithFirebase(
          email,
          password
        );
        if (idToken) {
          localStorage.setItem("idToken", idToken);
          localStorage.setItem("firebaseUid", userCredential?.user?.uid);
          localStorage.setItem("email", userCredential?.user?.email);
          localStorage.setItem("userName", userCredential?.user?.displayName);
          localStorage.setItem("audioOn", "true");

          // const settings = await fetchUserSettings();

          // const fcmToken = localStorage.getItem("fcmToken");

          // if (
          //   settings?.notification &&
          //   fcmToken &&
          //   fcmToken !== "ios-safari-not-supported"
          // ) {
          //   const appName = process.env.APP;
          //   ["english", "hindi", "bengali"].forEach((lang) => {
          //     if (settings.notification[lang]) {
          //       subscribeTopic(fcmToken, `${appName}_${lang.toUpperCase()}`);
          //     }
          //   });
          //   subscribeTopic(fcmToken, `${appName}_GENERAL}`);

          //   if (Array.isArray(settings?.fcm)) {
          //     settings.fcm.push({
          //       lastModificationTime: Date.now(),
          //       token: fcmToken,
          //     });
          //   } else {
          //     settings.fcm = [
          //       {
          //         lastModificationTime: Date.now(),
          //         token: fcmToken,
          //       },
          //     ];
          //   }

          //   await updateUserSettings(settings);
          // }
          handleNotificationPermission()

          message.success("Login successful. Welcome back!");

          // Show sync loader
          setShowSyncLoader(true);

          try {
            // Start syncing lectures
            await syncData();
          } catch (syncError) {
            console.error("Error syncing lectures:", syncError);
            // Continue to media library even if sync fails
          } finally {
            // Hide sync loader and redirect
            setShowSyncLoader(false);
            setIsTabChangeLoading(true); // Set loading state for tab change
            router.push("/media-library");
          }
        }
      }
    } catch (error: any) {
      if (error.message === "Firebase: Error (auth/user-not-found).") {
        message.error(
          "Email not recognized. Please enter a registered email address."
        );
      } else {
        message.error(
          "Oops! Something went wrong. Please try again in a moment."
        );
      }
    } finally {
      setLoader(false);
    }
  };

  const handleNotificationPermission = async () => {
    try {
      const settings = await fetchUserSettings();

        const fcmToken = localStorage.getItem("fcmToken");

          if (
            settings?.notification &&
            fcmToken &&
            fcmToken !== "ios-safari-not-supported"
          ) {
            const appName = process.env.APP;
            ["english", "hindi", "bengali"].forEach((lang) => {
              if (settings.notification[lang]) {
                subscribeTopic(fcmToken, `${appName}_${lang.toUpperCase()}`);
              }
            });
            subscribeTopic(fcmToken, `${appName}_GENERAL}`);

            if (Array.isArray(settings?.fcm)) {
              settings.fcm.push({
                lastModificationTime: Date.now(),
                token: fcmToken,
              });
            } else {
              settings.fcm = [
                {
                  lastModificationTime: Date.now(),
                  token: fcmToken,
                },
              ];
            }

            await updateUserSettings(settings);
          }
    } catch (error) {
      console.error("Error fetching settings:", error);
      
    }
  }

  const handleGoogleLogin = async () => {
    try {
      const user: any = await loginWithGoogle();
      if (user) {
       // Show sync loader
        setShowSyncLoader(true);
        localStorage.setItem("idToken", user?.accessToken);
        localStorage.setItem("firebaseUid", user?.uid);
        localStorage.setItem("email", user?.email);
        localStorage.setItem("userName", user?.displayName);
        localStorage.setItem("audioOn", "true");

        handleNotificationPermission()

        message.success("Successfully logged in.");

        try {
          // Start syncing lectures
          await syncData();
        } catch (syncError) {
          console.error("Error syncing lectures:", syncError);
          // Continue to media library even if sync fails
        } finally {
          // Hide sync loader and redirect
          setShowSyncLoader(false);
          setIsTabChangeLoading(true);
          router.push("/media-library");
        }
      }
    } catch (error) {
      console.log("error", error);
      message.error(
        "Oops! Something went wrong. Please try again in a moment."
      );
    }
  };

  const handleSignInWithApple = async () => {
    try {
      const res: any = await loginWithApple();
      if (res?.user) {
        localStorage.setItem("idToken", res?.idToken);
        localStorage.setItem("firebaseUid", res?.user?.uid);
        localStorage.setItem("email", res?.user?.email);
        localStorage.setItem("userName", res?.user?.displayName);
        localStorage.setItem("audioOn", "true");

        // const settings = await fetchUserSettings();

        // if (settings?.notification) {
        //   const appName = process.env.APP;
        //   ["english", "hindi", "bengali"].forEach((lang) => {
        //     if (settings.notification[lang]) {
        //       subscribeTopic(fcmToken, `${appName}_${lang.toUpperCase()}`);
        //     }
        //   });

        //   if (Array.isArray(settings?.fcm)) {
        //     settings.fcm.push({
        //       lastModificationTime: Date.now(),
        //       token: fcmToken,
        //     });
        //   } else {
        //     settings.fcm = [
        //       {
        //         lastModificationTime: Date.now(),
        //         token: fcmToken,
        //       },
        //     ];
        //   }

        //   await updateUserSettings(settings);
        // }
        handleNotificationPermission()

        message.success("Successfully logged in.");

        // Show sync loader
        setShowSyncLoader(true);

        try {
          // Start syncing lectures
          await syncData();
        } catch (syncError) {
          console.error("Error syncing lectures:", syncError);
          // Continue to media library even if sync fails
        } finally {
          // Hide sync loader and redirect
          setShowSyncLoader(false);
          setIsTabChangeLoading(true);
          router.push("/media-library");
        }
      }
    } catch (error) {
      console.log("error", error);
      message.error(
        "Oops! Something went wrong. Please try again in a moment."
      );
    }
  };

  return (
    <div
      className={`w-full h-full flex justify-center items-center overflow-y-auto ${roboto.className}`}
    >
      {/* Full-screen loader for syncing lectures */}
      <FullScreenLoader
        visible={showSyncLoader || fcmLoader}
        message={fcmLoader ? "Loading..." : "Syncing lectures..."}
      />
      <div
        className="w-[90%] sm:w-[424px] rounded-[12px] my-6 p-6"
        style={{
          boxShadow: "0 4px 24px #0000001f",
        }}
      >
        <div className="w-full flex justify-center">
          <Image
            src="/images/auth/ava.jpg"
            width={88}
            height={88}
            alt=""
            className="w-[88px] h-[88px] mb-5"
          />
        </div>

        <h3 className="text-[28px] font-[600] leading-[100%] text-center mb-10">
          {appConfig.appTitle}
        </h3>

        <div className="flex flex-col gap-5 mb-6">
          {/* Email */}
          <div className="relative">
            <Input
              size="large"
              placeholder="E-mail"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="h-[44px] !rounded-[10px] !text-sm"
              status={
                submitted && (!validateEmail(email) || email === "")
                  ? "error"
                  : undefined
              }
              disabled={loader}
            />
            {submitted && !validateEmail(email) && email !== "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                Invalid Email
              </p>
            )}
            {submitted && email === "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                Email is required
              </p>
            )}
          </div>

          {/* Password */}
          <div className="relative">
            <Input.Password
              size="large"
              placeholder="Password"
              visibilityToggle={false}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="h-[44px] !rounded-[10px] !text-sm"
              status={
                submitted &&
                (password.trim().length < 8 || password.trim() === "")
                  ? "error"
                  : undefined
              }
              onPressEnter={handleSignIn}
              disabled={loader}
            />
            {submitted && password.trim().length < 8 && password !== "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                Password must be at least 8 characters
              </p>
            )}
            {submitted && password === "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                Password is required
              </p>
            )}
          </div>
        </div>

        {/* Sign In Button */}
        <Button
          className={`w-full !text-white !font-[500] !border-none ${
            email.trim() === "" || password.trim() === ""
              ? "!bg-primary-light"
              : "!bg-primary"
          }`}
          style={{
            height: appConfig.ui.buttonHeight,
            fontSize: "17px",
            borderRadius: appConfig.ui.borderRadius.small,
          }}
          onClick={handleSignIn}
          disabled={email.trim() === "" || password.trim() === ""}
          loading={loader}
        >
          Sign In
        </Button>

        <div className="flex justify-center my-5">
          <Link
            href={"/restore"}
            className="text-sm border-b border-dashed cursor-pointer"
          >
            Forgot your password?
          </Link>
        </div>

        <div className="flex justify-center my-8">
          <h4 className="text-sm text-[#1b1f3b66] leading-[18px]">
            Not have an account yet?{" "}
            <Link
              href={"/register"}
              className="text-black border-b border-dashed cursor-pointer"
            >
              Create one
            </Link>
          </h4>
        </div>

        <div className="flex items-center w-full my-8">
          <hr className="flex-grow border-t border-gray-300" />
          <span className="mx-4 text-gray-500 text-sm">or</span>
          <hr className="flex-grow border-t border-gray-300" />
        </div>

        <div className="flex gap-2 justify-between sm:px-6 mb-4">
          {/* Continue with Google */}
          <Button
            className={`w-[78%] !font-[600] !text-[.9375rem] !bg-white !flex gap-2`}
            style={{
              height: appConfig.ui.buttonHeight,
              fontSize: "17px",
              borderRadius: appConfig.ui.borderRadius.small,
              border: "1px solid #d0d8f6",
            }}
            onClick={handleGoogleLogin}
          >
            <Image
              src="/images/auth/google.png"
              alt="google"
              width={22}
              height={22}
              className="w-[22px] h-[22px]"
            />
            Continue with Google
          </Button>

          {/* Continue with Apple */}
          {appConfig.features.enableAppleLogin && (
            <Button
              className={`w-[44px] !font-[600] !text-[.9375rem] !bg-white !flex gap-2`}
              style={{
                height: appConfig.ui.buttonHeight,
                fontSize: "17px",
                borderRadius: appConfig.ui.borderRadius.small,
                border: "1px solid #d0d8f6",
              }}
              onClick={handleSignInWithApple}
              title="Continue with Apple"
            >
              <AppleIcon style={{ fontSize: "24px", color: "black" }} />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Login;
