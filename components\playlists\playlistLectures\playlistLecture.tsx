"use client";
import React, { useC<PERSON>back, useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Open_Sans, Inter, Roboto, Poppins } from "next/font/google";
import { usePara<PERSON>, useRouter } from "next/navigation";
import PlaybackMode from "@/components/helperComponents/playbackMode";
import SortBy from "@/components/helperComponents/sortBy";
import Filter from "@/components/helperComponents/filter";
import SelectFiles from "@/components/helperComponents/selectFile";
import AddToFavorites from "@/components/helperComponents/addToFavorite";
import MarkAsComplete from "@/components/helperComponents/markAsComplete";
import { CloseOutlined } from "@ant-design/icons";
import { useFilterContext } from "@/src/context/filter.context";
import { useSearchParams } from "next/navigation";
import { Button, message } from "antd";
import { fetchPlaylistLectures } from "@/src/services/playlist.service";
import { useSearch<PERSON>ontext } from "@/src/context/search.context";
import LectureCard from "@/components/lectureCard/lectureCard";
import LectureCardSkeleton from "@/components/lectureCard/lectureCardSkeleton";
import InfiniteScroll from "react-infinite-scroll-component";
import AudioToggleSwitch from "@/components/helperComponents/audioToggleSwitch";
import EditPlaylistModal from "@/components/Modal/createPlaylistModal";
import { IoArrowBack } from "react-icons/io5";
import RemoveFavorites from "@/components/helperComponents/removeFavourite";
import ResetAsComplete from "@/components/helperComponents/resetAsComplete";

const open_Sans = Open_Sans({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});
const poppins = Poppins({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});
const roboto = Roboto({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});

const PlaylistLectures = () => {
    const router = useRouter();
    const params = useParams();
    const { id } = params;
    const searchParams = useSearchParams();

    const { search, isSearching, setIsSearching, searchQuery } =
        useSearchContext();
    const {
        isFiltering,
        setIsFiltering,
        sortBy,
        selectedFilterValues,
        playbackMode,
    } = useFilterContext();

    const [playlistData, setPlaylistData] = useState<any>(null);
    const [allLectures, setAllLectures] = useState<any[]>([]);
    const [displayedLectures, setDisplayedLectures] = useState<any[]>([]);
    const [isSelectFileOpen, setIsSelectFileOpen] = useState(false);
    const [selectedFiles, setSelectedFiles] = useState([]);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [isEditPlaylistModalOpen, setIsEditPlaylistModalOpen] =
        useState(false);

    const [loading, setLoading] = useState(false);
    const [isSearchLoading, setIsSearchLoading] = useState(false);
    const [hasMore, setHasMore] = useState(false);
    const [page, setPage] = useState(1);
    const resultsPerPage = 20;

    const isFirstRender = useRef(true);
    const isSearchInitialMount = useRef(true);

    // Load initial favorites data
    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            // Using useSearchParams from next/navigation
            const dataParam = searchParams.get("data");

            let data = null;
            if (dataParam) {
                try {
                    data = JSON.parse(decodeURIComponent(dataParam));
                    setPlaylistData(data);
                    loadPlaylistLectures(data);
                } catch (e) {
                    console.error("Failed to parse data param:", e);
                }
            }
        }
    }, []);

    // Handle search
    useEffect(() => {
        if (isSearchInitialMount.current) {
            isSearchInitialMount.current = false;
            return;
        }

        if (isSearching) {
            handleSearch();
        } else {
            const dataParam = searchParams.get("data");

            loadPlaylistLectures(
                dataParam
                    ? JSON.parse(decodeURIComponent(dataParam))
                    : playlistData
            );
        }
    }, [search]);

    // Handle filter/sort changes
    useEffect(() => {
        if (!isFirstRender.current) {
            if (isSearching) {
                // Re-apply current search with new filters/sort
                handleSearch();
            } else {
                // Re-load favorites with new filters/sort
                const dataParam = searchParams.get("data");

                loadPlaylistLectures(
                    dataParam
                        ? JSON.parse(decodeURIComponent(dataParam))
                        : playlistData
                );
            }
        }
    }, [sortBy, selectedFilterValues, playbackMode]);

    const loadPlaylistLectures = async (data: any = playlistData) => {
        try {
            setLoading(true);

            const lectures = await fetchPlaylistLectures({
                searchQuery: isSearching ? searchQuery : "",
                sortBy,
                allFilter: selectedFilterValues,
                playbackMode,
                lectureIds: data?.lectureIds || [],
            });

            setAllLectures(lectures);

            // Reset pagination and load first page
            setPage(1);
            const firstPageLectures = lectures.slice(0, resultsPerPage);
            setDisplayedLectures(firstPageLectures);
            setHasMore(lectures.length > resultsPerPage);
        } catch (error) {
            console.error("Error loading playlist lectures:", error);
            message.error(
                "We couldn't fetch playlist lectures at this moment, please try again later."
            );
            setAllLectures([]);
            setDisplayedLectures([]);
            setHasMore(false);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = async (data: any = playlistData) => {
        resetSelectionState();

        if (!searchQuery.trim()) {
            setIsSearching(false);
            await loadPlaylistLectures();
            return;
        }

        try {
            setIsSearchLoading(true);
            setIsLoadingMore(true);
            setIsSearching(true);

            const lectures = await fetchPlaylistLectures({
                searchQuery,
                sortBy,
                allFilter: selectedFilterValues,
                playbackMode,
                lectureIds: data?.lectureIds || [],
            });

            setAllLectures(lectures);

            // Reset pagination and load first page
            setPage(1);
            const firstPageLectures = lectures.slice(0, resultsPerPage);
            setDisplayedLectures(firstPageLectures);
            setHasMore(lectures.length > resultsPerPage);
        } catch (error) {
            console.error("Error searching playlist lectures:", error);
            setAllLectures([]);
            setDisplayedLectures([]);
            setHasMore(false);
        } finally {
            setTimeout(() => {
                setIsLoadingMore(false);
                setIsSearchLoading(false);
            }, 800);
        }
    };

    const resetSelectionState = () => {
        setIsSelectFileOpen(false);
        setSelectedFiles([]);
    };

    const handleFilterChange = (values: any) => {
        resetSelectionState();

        if (!values || typeof values !== "object") {
            console.error("Invalid filter values:", values);
            return;
        }

        const hasActiveFilters = Object.values(values).some(
            (filterValues: any) =>
                Array.isArray(filterValues) && filterValues.length > 0
        );
        setIsFiltering(hasActiveFilters);
    };

    // Function to load more results for infinite scroll
    const loadMoreResults = useCallback(() => {
        if (!hasMore || isLoadingMore) return;

        setIsLoadingMore(true);

        setTimeout(() => {
            const nextPage = page + 1;
            const startIndex = (nextPage - 1) * resultsPerPage;
            const endIndex = startIndex + resultsPerPage;

            const nextResults = allLectures.slice(startIndex, endIndex);

            if (nextResults.length > 0) {
                setDisplayedLectures((prev) => [...prev, ...nextResults]);
                setPage(nextPage);
            }

            // Check if we've loaded all results
            if (endIndex >= allLectures.length) {
                setHasMore(false);
            }

            setIsLoadingMore(false);
        }, 500);
    }, [hasMore, isLoadingMore, page, allLectures, resultsPerPage]);

    // Function to handle favorite updates
    const handleLectureUpdated = async (
        updatedIds: (string | number)[],
        field: string,
        value: boolean
    ) => {
        if (updatedIds.length === 0) return;

        setAllLectures((prev: any) =>
            prev.map((lecture: any) => {
                if (updatedIds.includes(lecture.id)) {
                    return { ...lecture, [field]: value };
                }
                return lecture;
            })
        );

        setDisplayedLectures((prevResults) =>
            prevResults.map((lecture: any) => {
                if (updatedIds.includes(lecture.id)) {
                    return { ...lecture, [field]: value };
                }
                return lecture;
            })
        );
    };

    // Handle back button click
    const handleBackClick = () => {
        router.push(`/playlists`);
    };

    return (
        <div className={`w-full h-full ${roboto.className}`}>
            <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b relative">
                {/* Result and selected count */}
                <div className="hidden md:flex gap-4 md:absolute top-5 min-[460px]:top-2 right-4">
                    {isSelectFileOpen && (
                        <h2
                            className={`text-[12px] leading-5 font-[400] text-text-primary`}
                        >
                            <span className="font-[500]">Selected:</span>{" "}
                            {selectedFiles.length}
                        </h2>
                    )}
                    {(isSearching || isFiltering) && (
                        <h2
                            className={`text-[12px] leading-5 font-[400] text-text-primary `}
                        >
                            <span className="font-[500]">Result count:</span>{" "}
                            {allLectures.length.toString()}
                        </h2>
                    )}
                </div>

                {/* Heading */}
                <div className="flex justify-center items-center">
                    <button
                        type="button"
                        onClick={handleBackClick}
                        className="mr-3 text-primary text-opacity-90 hover:text-opacity-100 transition-colors"
                        aria-label="Go back"
                        title="Back"
                    >
                        <IoArrowBack size={24} />
                    </button>
                    {/* Heading */}
                    <h1 className="max-[392px]:text-[24px] max-[460px]:text-[24px] text-[28px] font-[600] leading-8">
                        {playlistData?.title || "Playlist Lectures"}
                    </h1>
                </div>
                <div className="flex gap-2 items-end">
                    {!isSelectFileOpen ? (
                        <>
                            <AudioToggleSwitch />
                            <PlaybackMode />
                            <SortBy />
                            <Filter {...{ handleFilterChange }} />
                            <SelectFiles {...{ setIsSelectFileOpen }} />
                        </>
                    ) : (
                        <>
                            <AddToFavorites
                                selectedFiles={selectedFiles}
                                onFavoritesUpdated={handleLectureUpdated}
                            />
                            <RemoveFavorites
                                selectedFiles={selectedFiles}
                                onFavoritesUpdated={handleLectureUpdated}
                            />
                            <MarkAsComplete
                                {...{
                                    selectedFiles,
                                    onCompleteUpdated: handleLectureUpdated,
                                }}
                            />
                            <ResetAsComplete
                                {...{
                                    selectedFiles,
                                    onCompleteUpdated: handleLectureUpdated,
                                }}
                            />
                            <div
                                className="h-[32px] flex gap-2 items-center pt-[2px] px-2.5 md:px-3 text-[13px] text-left bg-[#E0E0E0] rounded-[12px] hover:opacity-85 cursor-pointer transition-all"
                                onClick={resetSelectionState}
                            >
                                <h2 className="text-[13px] leading-5 font-[400] text-text-primary">
                                    <span className="sm:block hidden">
                                        Cancel
                                    </span>{" "}
                                    <span className="block sm:hidden">
                                        <CloseOutlined />
                                    </span>
                                </h2>
                            </div>
                        </>
                    )}
                </div>
            </div>
            <div
                id="scrollableDiv"
                className="w-full h-[calc(100%-60px)] md:h-[calc(100%-84px)] p-5 pb-40 !overflow-x-hidden overflow-y-auto scrollbar"
            >
                <div
                    className={`flex gap-10 items-start flex-wrap mb-4 ${poppins.className}`}
                >
                    <div className="flex flex-col gap-1">
                        <h2 className="text-[11px] leading-4 font-[400] text-text-primary">
                            Date created
                        </h2>
                        <p className="text-[13px] leading-5 font-[400] text-text">
                            {playlistData?.creationTime || "No date available"}
                        </p>
                    </div>
                    <div className="flex flex-col gap-1">
                        <h2 className="text-[11px] leading-4 font-[400] text-text-primary">
                            Category
                        </h2>
                        <p className="text-[13px] leading-5 font-[400] text-text">
                            {playlistData?.category || "No category available"}
                        </p>
                    </div>
                    <div className="flex flex-col gap-1">
                        <h2 className="text-[11px] leading-4 font-[400] text-text-primary">
                            Lectures
                        </h2>
                        <p className="text-[13px] leading-5 font-[400] text-text">
                            {playlistData?.lectureCount ||
                                "No lectures available"}
                        </p>
                    </div>
                    <div className="flex flex-col gap-1">
                        <h2 className="text-[11px] leading-4 font-[400] text-text-primary">
                            Description
                        </h2>
                        <p className="text-[13px] leading-5 font-[400] text-text">
                            {playlistData?.description ||
                                "No description available"}
                        </p>
                    </div>
                </div>

                {!isSearching && !isFiltering ? (
                    <div className="w-full flex flex-col gap-4">
                        {loading ? (
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                {Array(15)
                                    .fill(0)
                                    .map((_, index) => (
                                        <LectureCardSkeleton
                                            key={`favorites-skeleton-${index}`}
                                        />
                                    ))}
                            </div>
                        ) : allLectures.length === 0 ? (
                            <div className="text-center py-20">
                                <p className="text-lg text-gray-500">
                                    No Lectures found
                                </p>
                                <p className="text-sm text-gray-400 mt-2">
                                    You haven't added any lectures to this playlist
                                    yet
                                </p>
                            </div>
                        ) : (
                            <InfiniteScroll
                                dataLength={displayedLectures.length}
                                next={loadMoreResults}
                                hasMore={hasMore}
                                loader={
                                    <div className="w-full grid grid-cols-5 gap-5 mt-4">
                                        {Array(5)
                                            .fill(0)
                                            .map((_, index) => (
                                                <LectureCardSkeleton
                                                    key={`favorites-skeleton-${index}`}
                                                />
                                            ))}
                                    </div>
                                }
                                endMessage={null}
                                scrollableTarget="scrollableDiv"
                                scrollThreshold={0.9}
                                className="!overflow-x-hidden"
                            >
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                    {displayedLectures.map((lecture: any) => (
                                        <LectureCard
                                            key={lecture.id}
                                            lectureData={lecture}
                                            {...{
                                                isSelectFileOpen,
                                                selectedFiles,
                                                setSelectedFiles,
                                                onLectureUpdated:
                                                    handleLectureUpdated,
                                            }}
                                            playlistData={playlistData}
                                            setPlaylistData={setPlaylistData}
                                            setAllLectures={setAllLectures}
                                            setDisplayedLectures={
                                                setDisplayedLectures
                                            }
                                        />
                                    ))}
                                </div>
                            </InfiniteScroll>
                        )}
                    </div>
                ) : (
                    <div className="w-full flex flex-col gap-4">
                        <div className="flex flex-col gap-1">
                            <h1 className="text-[22px] min-[460px]:text-[24px] font-[600] leading-8">
                                {isSearching
                                    ? `Search Results for "${searchQuery}"`
                                    : "Filtered Results"}
                            </h1>
                            {(() => {
                                // Check if any filters are applied
                                const hasFilters = Object.values(
                                    selectedFilterValues
                                ).some(
                                    (values: any) =>
                                        Array.isArray(values) &&
                                        values.length > 0
                                );

                                if (hasFilters) {
                                    return (
                                        <div className="flex flex-wrap items-center gap-2 mb-2">
                                            <span className="text-sm text-gray-600">
                                                Filters applied:
                                            </span>
                                            {Object.entries(
                                                selectedFilterValues
                                            ).map(
                                                ([filterType, values]: [
                                                    string,
                                                    any
                                                ]) => {
                                                    if (
                                                        Array.isArray(values) &&
                                                        values.length > 0
                                                    ) {
                                                        return (
                                                            <span
                                                                key={filterType}
                                                                className="text-[10px] bg-primary-light text-primary px-2 py-1 rounded-md"
                                                            >
                                                                {filterType}:{" "}
                                                                {values.length}
                                                            </span>
                                                        );
                                                    }
                                                    return null;
                                                }
                                            )}
                                        </div>
                                    );
                                }
                                return null;
                            })()}
                        </div>

                        {isSearchLoading ? (
                            // Show skeleton loading during deep search
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                {Array(15)
                                    .fill(0)
                                    .map((_, index) => (
                                        <LectureCardSkeleton
                                            key={`deep-search-skeleton-${index}`}
                                        />
                                    ))}
                            </div>
                        ) : displayedLectures.length > 0 ? (
                            <InfiniteScroll
                                dataLength={displayedLectures.length}
                                next={loadMoreResults}
                                hasMore={hasMore}
                                loader={
                                    <div className="w-full grid grid-cols-5 gap-5 mt-4">
                                        {Array(5)
                                            .fill(0)
                                            .map((_, index) => (
                                                <LectureCardSkeleton
                                                    key={`search-skeleton-${index}`}
                                                />
                                            ))}
                                    </div>
                                }
                                endMessage={null}
                                scrollableTarget="scrollableDiv"
                                scrollThreshold={0.9}
                                className="!overflow-x-hidden"
                            >
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                    {displayedLectures.map((lecture: any) => (
                                        <LectureCard
                                            key={lecture.id}
                                            lectureData={lecture}
                                            {...{
                                                isSelectFileOpen,
                                                selectedFiles,
                                                setSelectedFiles,
                                                onLectureUpdated:
                                                    handleLectureUpdated,
                                            }}
                                            playlistData={playlistData}
                                            setPlaylistData={setPlaylistData}
                                            setAllLectures={setAllLectures}
                                            setDisplayedLectures={
                                                setDisplayedLectures
                                            }
                                        />
                                    ))}
                                </div>
                            </InfiniteScroll>
                        ) : (
                            <div className="text-center py-10">
                                <p className="text-lg text-gray-500">
                                    {isSearching
                                        ? `No results found for "${searchQuery}"${
                                              isFiltering
                                                  ? " with the applied filters"
                                                  : ""
                                          }`
                                        : "No results match the applied filters"}
                                </p>
                                <p className="text-sm text-gray-400 mt-2">
                                    {isSearching
                                        ? "Try different keywords or check your spelling"
                                        : "Try adjusting your filter criteria"}
                                </p>
                            </div>
                        )}
                    </div>
                )}
            </div>

            {isEditPlaylistModalOpen && (
                <EditPlaylistModal
                    isModalOpen={isEditPlaylistModalOpen}
                    setIsModalOpen={setIsEditPlaylistModalOpen}
                    editData={{
                        listID: playlistData?.listID,
                        title: playlistData?.title,
                        lecturesCategory: playlistData?.category,
                        description: playlistData?.description,
                        listType: playlistData?.listType,
                    }}
                    onEditSuccess={(updatedData: any) => {
                        const newPlaylistData = {
                            ...playlistData,
                            title: updatedData.title,
                            category: updatedData.lecturesCategory,
                            description: updatedData.discription,
                        };
                        setPlaylistData(newPlaylistData);

                        const encoded = encodeURIComponent(
                            JSON.stringify(newPlaylistData)
                        );
                        router.replace(`?data=${encoded}`, { scroll: false });
                    }}
                />
            )}
        </div>
    );
};

export default PlaylistLectures;
