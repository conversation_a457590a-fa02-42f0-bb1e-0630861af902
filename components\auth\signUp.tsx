"use client";
import React, { useEffect, useRef, useState } from "react";
import { Open_Sans, Inter, Roboto } from "next/font/google";
import { Button, Input, message } from "antd";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeftOutlined } from "@ant-design/icons";
import {
  createUserInUserCollection,
  signUpWithFirebase,
} from "@/src/api/auth.api";
import { useIndexedDBContext } from "@/src/context/indexedDB.context";
import FullScreenLoader from "../ui/fullScreenLoader";
import { useSidebarContext } from "@/src/context/sidebar.context";
import { getFCMToken } from "@/src/config/firebase.config";
import appConfig from "@/src/config/apps";
import { subscribeTopic } from "@/src/api/settings.api";

const open_Sans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const inter = Inter({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const SignUp = () => {
  const router = useRouter();
  const { syncData, isSyncing } = useIndexedDBContext();
  const { setIsTabChangeLoading } = useSidebarContext();
  const [firstName, setFirstName] = useState("");
  const [surname, setSurname] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const [submitted, setSubmitted] = useState(false);
  const [loader, setLoader] = useState(false);
  const [showSyncLoader, setShowSyncLoader] = useState(false);
  const [fcmLoader, setFcmLoader] = useState(true);

  const [fcmToken, setFcmToken] = useState("");
  const isInitialized = useRef(false);

  useEffect(() => {
    const registerServiceWorkerAndGetToken = async () => {
      try {
        setFcmLoader(true);

        // Check if service worker is already registered
        let registration = await navigator.serviceWorker.getRegistration(
          "/firebase-messaging-sw.js"
        );

        if (!registration) {
          // Register the service worker if not already registered
          console.log("Registering new service worker...");
          registration = await navigator.serviceWorker.register(
            "/firebase-messaging-sw.js",
            { scope: "/" }
          );
          console.log("Service Worker registered:", registration);
        } else {
          console.log("Service Worker already registered:", registration);
        }

        // Wait for the service worker to be ready
        await navigator.serviceWorker.ready;
        console.log("Service Worker is ready");

        // Get the FCM token
        const token = await getFCMToken(registration);

        if (token) {
          console.log("FCM Token retrieved:", token);

          // Handle iOS Safari placeholder token
          if (token === "ios-safari-not-supported") {
            console.warn("Using placeholder token for iOS Safari");
            localStorage.setItem("fcmToken", token);
            setFcmToken(token);
            // Show a message to the user that notifications may not work on iOS Safari
          } else {
            localStorage.setItem("fcmToken", token);
            setFcmToken(token);
          }
        } else {
          console.error("No FCM Token available.");
          localStorage.setItem("fcmToken", "");
        }
      } catch (error) {
        console.error("Error during Service Worker or FCM setup:", error);
        localStorage.setItem("fcmToken", "");
      } finally {
        setFcmLoader(false);
      }
    };

    if (!isInitialized.current && "serviceWorker" in navigator) {
      isInitialized.current = true;
      const fcm = localStorage.getItem("fcmToken");
      if (fcm) {
        setFcmToken(fcm);
        setFcmLoader(false);
      } else {
        registerServiceWorkerAndGetToken();
      }
    }
  }, []);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSignUp = async () => {
    try {
      setSubmitted(true);

      if (
        firstName.trim() !== "" &&
        surname.trim() !== "" &&
        validateEmail(email) &&
        password.trim().length >= 8 &&
        confirmPassword === password
      ) {
        setLoader(true);

        const res: any = await signUpWithFirebase(email, password);
        if (res) {
          const payload = {
            lastModificationTime: Date.now(),
            notification: {
              english: false,
              hindi: false,
              bengali: false,
            },
            fcm: [] as { lastModificationTime: number; token: string }[],
          };

          const fcmToken = localStorage.getItem("fcmToken");

          if (fcmToken && fcmToken !== "ios-safari-not-supported") {
            const subscribeResults = await Promise.all(
              appConfig.topics.map((topic: any) =>
                subscribeTopic(fcmToken, topic)
              )
            );

            const appName = process.env.APP;

            subscribeResults.forEach((res: any) => {
              if (res?.success) {
                switch (res?.topic) {
                  case `${appName}_ENGLISH`:
                    payload.notification.english = true;
                    break;
                  case `${appName}_HINDI`:
                    payload.notification.hindi = true;
                    break;
                  case `${appName}_BENGALI`:
                    payload.notification.bengali = true;
                    break;
                  default:
                    break;
                }
              }
            });

            payload.fcm.push({
              lastModificationTime: Date.now(),
              token: fcmToken,
            });
          }

          console.log("payload", payload);

          createUserInUserCollection(res?.uid, payload);

          localStorage.setItem("idToken", res?.accessToken);
          localStorage.setItem("firebaseUid", res?.uid);
          localStorage.setItem("email", res?.email);
          if (res?.displayName) {
            localStorage.setItem("userName", res?.displayName);
          } else {
            localStorage.setItem("userName", firstName + " " + surname);
          }
          localStorage.setItem("audioOn", "true");
          message.success("You're all set! Your account has been created.");

          // Show sync loader
          setShowSyncLoader(true);

          try {
            // Start syncing lectures
            await syncData();
          } catch (syncError) {
            console.error("Error syncing lectures:", syncError);
            // Continue to media library even if sync fails
          } finally {
            // Hide sync loader and redirect
            setShowSyncLoader(false);
            setIsTabChangeLoading(true);
            router.push("/media-library");
          }
        }
      }
    } catch (error: any) {
      console.log("error", error);
      if (error?.message === "Firebase: Error (auth/email-already-in-use).") {
        message.error(
          "Email already registered. Try logging in or use a different email."
        );
      } else {
        message.error(
          "Oops! Something went wrong. Please try again in a moment."
        );
      }
    } finally {
      setLoader(false);
    }
  };

  return (
    <div
      className={`w-full h-full flex justify-center items-center ${roboto.className}`}
    >
      {/* Full-screen loader for syncing lectures */}
      <FullScreenLoader
        visible={showSyncLoader || fcmLoader}
        message={fcmLoader ? "Loading..." : "Syncing lectures..."}
      />
      <div
        className="w-[90%] sm:w-[424px] rounded-[12px] my-6 p-6 relative"
        style={{
          boxShadow: "0 4px 24px #0000001f",
        }}
      >
        <Link
          href={"/login"}
          className="absolute top-6 left-6 text-sm flex gap-2 hover:opacity-60 transition-all"
        >
          <ArrowLeftOutlined className="text-[18px]" />
          Back
        </Link>

        <h3 className="text-[28px] font-[600] leading-[100%] text-center mt-14 mb-6">
          Create an account
        </h3>

        <div className="flex flex-col gap-5 mb-6">
          {/* First Name */}
          <div className="relative">
            <Input
              size="large"
              placeholder="First name"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="h-[44px] !rounded-[10px] !text-sm"
              status={
                submitted && firstName.trim() === "" ? "error" : undefined
              }
              disabled={loader}
            />
            {submitted && firstName.trim() === "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                First name is required
              </p>
            )}
          </div>

          {/* Surname */}
          <div className="relative">
            <Input
              size="large"
              placeholder="Surname"
              value={surname}
              onChange={(e) => setSurname(e.target.value)}
              className="h-[44px] !rounded-[10px] !text-sm"
              status={submitted && surname.trim() === "" ? "error" : undefined}
              disabled={loader}
            />
            {submitted && surname.trim() === "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                Surname is required
              </p>
            )}
          </div>

          {/* Email */}
          <div className="relative">
            <Input
              size="large"
              placeholder="E-mail"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="h-[44px] !rounded-[10px] !text-sm"
              status={
                submitted && (!validateEmail(email) || email === "")
                  ? "error"
                  : undefined
              }
              disabled={loader}
            />
            {submitted && !validateEmail(email) && email !== "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                Invalid Email
              </p>
            )}
            {submitted && email === "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                Email is required
              </p>
            )}
          </div>

          {/* Password */}
          <div className="relative">
            <Input.Password
              size="large"
              placeholder="Password"
              visibilityToggle={false}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="h-[44px] !rounded-[10px] !text-sm"
              status={
                submitted &&
                (password.trim().length < 8 || password.trim() === "")
                  ? "error"
                  : undefined
              }
              disabled={loader}
            />
            {submitted && password.trim().length < 8 && password !== "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                Password must be at least 8 characters
              </p>
            )}
            {submitted && password === "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                Password is required
              </p>
            )}
          </div>

          {/* Confirm Password */}
          <div className="relative">
            <Input.Password
              size="large"
              placeholder="Confirm password"
              visibilityToggle={false}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="h-[44px] !rounded-[10px] !text-sm"
              status={
                submitted &&
                (confirmPassword !== password || confirmPassword === "")
                  ? "error"
                  : undefined
              }
              onPressEnter={handleSignUp}
              disabled={loader}
            />
            {submitted &&
              confirmPassword !== password &&
              confirmPassword !== "" && (
                <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                  Passwords must match
                </p>
              )}
            {submitted && confirmPassword === "" && (
              <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                Confirm password is required
              </p>
            )}
          </div>
        </div>

        {/* Sign Up Button */}
        <Button
          className={`w-full !text-white !font-[500] !border-none ${
            firstName.trim() === "" ||
            surname.trim() === "" ||
            email.trim() === "" ||
            password.trim() === "" ||
            confirmPassword.trim() === ""
              ? "!bg-primary-light"
              : "!bg-primary"
          }`}
          style={{
            height: "44px",
            fontSize: "17px",
            borderRadius: "10px",
          }}
          onClick={handleSignUp}
          disabled={
            firstName.trim() === "" ||
            surname.trim() === "" ||
            email.trim() === "" ||
            password.trim() === "" ||
            confirmPassword.trim() === ""
          }
          loading={loader}
        >
          Sign Up
        </Button>

        <div className="flex justify-center my-4">
          <h4 className="text-[12px] text-center text-[#1b1f3b66] leading-[18px]">
            By clicking Sign Up, you agree to our{" "}
            <Link
              href={appConfig?.privacyPolicy}
              target="_blank"
              className="text-primary cursor-pointer"
            >
              Private Data Policy
            </Link>{" "}
            and{" "}
            <Link
              href={appConfig?.cookiePolicy}
              target="_blank"
              className="text-primary cursor-pointer"
            >
              Cookie Policy
            </Link>
            .
          </h4>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
