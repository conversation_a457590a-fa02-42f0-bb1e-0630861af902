import L from "leaflet";
import { useEffect, useState } from "react";
import { useMap } from "react-leaflet";

// Apply filters to the data
export const applyFilters = (
  data: any[],
  filters: { [key: string]: string[] },
  playbackMode: number = 1
) => {
  // First apply playback mode filter
  let filteredData = data;

  // Apply playback mode filter (if not default)
  if (playbackMode === 2) {
    // Only lectures with video
    filteredData = data.filter((lecture) => {
      // Check if lecture has videos in resources
      return (
        lecture.resources &&
        lecture.resources.videos &&
        Array.isArray(lecture.resources.videos) &&
        lecture.resources.videos.length > 0
      );
    });
  }
  // For playbackMode === 1 (Audio only), show all lectures (no filtering needed)

  // If no category filters are selected, return the data filtered by playback mode
  const hasActiveFilters = Object.values(filters).some(
    (filterValues) => filterValues.length > 0
  );

  if (!hasActiveFilters) return filteredData;

  return filteredData.filter((lecture) => {
    // Check each filter category
    for (const [filterType, selectedValues] of Object.entries(filters)) {
      // Skip if no values are selected for this filter type
      if (selectedValues.length === 0) continue;

      // Check if the lecture matches any of the selected values for this filter type
      let matchesFilter = false;

      switch (filterType) {
        case "LANGUAGE":
          // Handle both lecture structures
          if (lecture.language && lecture.language.main) {
            // Original structure
            matchesFilter = selectedValues.some(
              (selectedLang) => lecture.language.main === selectedLang
            );
          } else if (lecture.language_main) {
            // Deep search structure
            matchesFilter = selectedValues.some(
              (selectedLang) => lecture.language_main === selectedLang
            );
          }
          break;

        case "COUNTRY":
          // Check if lecture's country matches any selected country
          if (lecture.location && lecture.location.country) {
            // Original structure
            matchesFilter = selectedValues.some(
              (selectedCountry) => lecture.location.country === selectedCountry
            );
          }
          // Deep search structure doesn't have country information
          break;

        case "PLACE":
          // Check if lecture's place matches any selected place
          if (lecture.place) {
            // Handle place as either string or array
            if (Array.isArray(lecture.place)) {
              // If place is an array, check if any element matches any selected place
              matchesFilter = lecture.place.some((placeItem: string) =>
                selectedValues.includes(placeItem)
              );
            } else {
              // If place is a string, check if it matches any selected place
              matchesFilter = selectedValues.some(
                (selectedPlace) => lecture.place === selectedPlace
              );
            }
          }
          break;

        case "YEAR":
          // Check if lecture's year matches any selected year
          if (lecture.dateOfRecording) {
            if (
              typeof lecture.dateOfRecording === "object" &&
              lecture.dateOfRecording.year
            ) {
              // Original structure
              const yearStr = lecture.dateOfRecording.year.toString();
              matchesFilter = selectedValues.some(
                (selectedYear) => yearStr === selectedYear
              );
            } else if (typeof lecture.dateOfRecording === "string") {
              // Deep search structure (YYYY-MM-DD)
              const yearStr = lecture.dateOfRecording.substring(0, 4);
              matchesFilter = selectedValues.some(
                (selectedYear) => yearStr === selectedYear
              );
            }
          }
          break;

        case "MONTH":
          // Check if lecture's month matches any selected month
          if (lecture.dateOfRecording) {
            if (
              typeof lecture.dateOfRecording === "object" &&
              lecture.dateOfRecording.month
            ) {
              // Original structure
              const month = lecture.dateOfRecording.month
                .toString()
                .padStart(2, "0");
              matchesFilter = selectedValues.some(
                (selectedMonth) => month === selectedMonth
              );
            } else if (
              typeof lecture.dateOfRecording === "string" &&
              lecture.dateOfRecording.length >= 7
            ) {
              // Deep search structure (YYYY-MM-DD)
              const month = lecture.dateOfRecording.substring(5, 7);
              matchesFilter = selectedValues.some(
                (selectedMonth) => month === selectedMonth
              );
            }
          }
          break;

        case "CATEGORY":
          // Check if any of lecture's categories match any selected category
          if (lecture.category) {
            if (Array.isArray(lecture.category)) {
              // If category is an array, check if any element matches any selected category
              matchesFilter = lecture.category.some((cat: string) =>
                selectedValues.includes(cat)
              );
            } else if (typeof lecture.category === "string") {
              // If category is a string, check if it matches any selected category
              matchesFilter = selectedValues.includes(lecture.category);
            }
          }
          break;

        // case "TRANSLATION":
        //   // Check if lecture has translations in any of the selected languages
        //   if (lecture.language) {
        //     // Check all language keys except 'main'
        //     const translationKeys = Object.keys(lecture.language).filter(
        //       (key) => key !== "main"
        //     );
        //     matchesFilter = translationKeys.some((key: string) =>
        //       selectedValues.includes(key)
        //     );
        //   }
        //   // Deep search structure doesn't have translation information
        //   break;

        case "TRANSLATION":
          if (
            lecture.language &&
            Array.isArray(lecture.language.translations)
          ) {
            matchesFilter = lecture.language.translations.some(
              (translation: string) => selectedValues.includes(translation)
            );
          }
          break;

        case "TYPE":
          if (selectedValues.includes("AUDIO")) {
            matchesFilter =
              !lecture.resources?.videos ||
              lecture.resources.videos.length === 0;
          } else if (selectedValues.includes("VIDEO")) {
            matchesFilter =
              lecture.resources?.videos && lecture.resources.videos.length > 0;
          }
          break;

        case "LENGTH":
          const lengths = Array.isArray(lecture.lengthType)
            ? lecture.lengthType
            : [lecture.lengthType];

          matchesFilter = lengths.some((l: string) =>
            selectedValues.includes(l.toUpperCase())
          );
          break;

        case "COMPLETED":
          if (selectedValues.includes("EXCLUDE COMPLETED")) {
            matchesFilter = !lecture.isCompleted;
          } else {
            matchesFilter = true;
          }
          break;

        case "TAG":
          const tags = Array.isArray(lecture.tags)
            ? lecture.tags
            : [lecture.tags];
          matchesFilter = tags?.some((tag: string) =>
            selectedValues.includes(tag)
          );
          break;

        default:
          matchesFilter = true;
          break;
      }

      // If lecture doesn't match this filter category, exclude it
      if (!matchesFilter) return false;
    }

    // If we get here, the lecture matched all applied filters
    return true;
  });
};

// Apply sorting to the data
export const applySorting = (data: any[], sortOption: number) => {
  if (!data || data.length === 0) return [];

  const sortedData = [...data];

  // Helper function to get date from either structure
  const getDateValue = (lecture: any): number => {
    if (!lecture.dateOfRecording) return 0;

    if (
      typeof lecture.dateOfRecording === "object" &&
      lecture.dateOfRecording.year
    ) {
      // Original structure
      return new Date(
        lecture.dateOfRecording.year,
        lecture.dateOfRecording.month - 1,
        lecture.dateOfRecording.day || 1
      ).getTime();
    } else if (typeof lecture.dateOfRecording === "string") {
      // Deep search structure (YYYY-MM-DD)
      return new Date(lecture.dateOfRecording).getTime();
    }
    return 0;
  };

  // Helper function to get title from either structure
  const getTitle = (lecture: any): string => {
    if (Array.isArray(lecture.title) && lecture.title.length > 0) {
      // Original structure
      return lecture.title[0];
    } else if (typeof lecture.title === "string") {
      // Deep search structure
      return lecture.title;
    }
    return "";
  };

  switch (sortOption) {
    case 0: // Default view (no sorting)
      return sortedData;

    case 1: // Duration (low to high)
      return sortedData.sort((a, b) => (a.length || 0) - (b.length || 0));

    case 2: // Duration (high to low)
      return sortedData.sort((a, b) => (b.length || 0) - (a.length || 0));

    case 9: // Listen (low to high)
      return sortedData.sort((a, b) => (a.listens || 0) - (b.listens || 0));

    case 10: // Listen (high to low)
      return sortedData.sort((a, b) => (b.listens || 0) - (a.listens || 0));

    case 5: // Rec date (oldest first)
      return sortedData.sort((a, b) => {
        const dateA = getDateValue(a);
        const dateB = getDateValue(b);
        return dateA - dateB;
      });

    case 6: // Rec date (latest first)
      return sortedData.sort((a, b) => {
        const dateA = getDateValue(a);
        const dateB = getDateValue(b);
        return dateB - dateA;
      });

    case 7: // Alphabetically (A - Z)
      return sortedData.sort((a, b) => {
        const titleA = getTitle(a);
        const titleB = getTitle(b);
        return titleA.localeCompare(titleB);
      });

    case 8: // Alphabetically (Z - A)
      return sortedData.sort((a, b) => {
        const titleA = getTitle(a);
        const titleB = getTitle(b);
        return titleB.localeCompare(titleA);
      });

    default:
      return sortedData;
  }
};

export const formatToK = (num: number): string => {
  if (num >= 1000) {
    const formatted = Math.floor(num / 100) / 10; // floor to 1 decimal place
    return formatted % 1 === 0
      ? `${formatted.toFixed(0)}k`
      : `${formatted.toFixed(1)}k`;
  }
  return num.toString();
};

export const formatDateRange = (start: number, end: number) => {
  const startDate = new Date(start);
  const endDate = new Date(end);

  return `${startDate.toLocaleDateString("en", {
    day: "2-digit",
    month: "short",
  })} - ${endDate.toLocaleDateString("en", {
    day: "2-digit",
    month: "short",
  })}`;
};

export const CenterMapOnSelected = ({
  selectedStop,
}: {
  selectedStop: any;
}) => {
  const map = useMap();
  const [leaflet, setLeaflet] = useState<any>(null);

  useEffect(() => {
    // Dynamically import Leaflet only in the browser
    import("leaflet").then((L) => {
      setLeaflet(L);
    });
  }, []);

  useEffect(() => {
    if (selectedStop && leaflet) {
      const targetLatLng = leaflet.latLng(
        selectedStop.venue.latitude,
        selectedStop.venue.longitude
      );

      const point = map.project(targetLatLng, map.getZoom());
      const offsetPoint = point.subtract([0, 0]); // shift left
      const offsetLatLng = map.unproject(offsetPoint, map.getZoom());

      map.flyTo(offsetLatLng, 10, {
        duration: 1.5,
      });
    }
  }, [selectedStop, map, leaflet]);

  return null;
};
