import { db } from "../config/firebase.config";
import {
  collection,
  query,
  orderBy,
  limit,
  getDocs,
  where,
  OrderByDirection,
  QueryConstraint,
  setDoc,
  doc,
} from "firebase/firestore";
import { getAllLectures } from "../services/indexedDB.service";
import { fetch } from "../libs/helper";

export interface FetchLecturesPayload {
  limit?: number;
  orderBy?: string;
  order?: OrderByDirection;
  language?: string;
  category?: string;
  useIndexedDB?: boolean; // Flag to determine data source
  searchQuery?: string; // Add search query parameter
  deepSearch?: boolean; // Flag for deep search
}

/**
 * Fetch lectures from IndexedDB or Firebase
 * By default, tries to fetch from IndexedDB first, then falls back to Firebase if needed
 */
export const fetchLectures = async (payload?: FetchLecturesPayload): Promise<any[]> => {
  try {
    // Set default values if payload is not provided
    const {
      limit: limitCount = 5,
      orderBy: orderByField = "dateOfRecording",
      order: orderDirection = "desc",
      language,
      category,
      useIndexedDB = true // Default to using IndexedDB
    } = payload || {};

    // Try to fetch from IndexedDB first if enabled
    if (useIndexedDB && typeof window !== 'undefined') {
      try {
        // Get all lectures from IndexedDB
        const allLectures = await getAllLectures();

        if (allLectures && allLectures.length > 0) {
          // Apply filters locally
          let filteredLectures = allLectures;

          // Filter by language if specified
          if (language) {
            filteredLectures = filteredLectures.filter(lecture =>
              lecture.language && lecture.language.main === language
            );
          }

          // Filter by category if specified
          if (category) {
            filteredLectures = filteredLectures.filter(lecture =>
              lecture.category && lecture.category.includes(category)
            );
          }

          // Sort the results locally
          filteredLectures.sort((a, b) => {
            // Special handling for dateOfRecording field
            if (orderByField === 'dateOfRecording') {
              // Helper function to get date value from dateOfRecording object
              const getDateValue = (lecture: any): number => {
                if (!lecture.dateOfRecording) return 0;

                if (typeof lecture.dateOfRecording === 'object' && lecture.dateOfRecording.year) {
                  // Original structure
                  return new Date(
                    lecture.dateOfRecording.year,
                    lecture.dateOfRecording.month - 1,
                    lecture.dateOfRecording.day || 1
                  ).getTime();
                } else if (typeof lecture.dateOfRecording === 'string') {
                  // Deep search structure (YYYY-MM-DD)
                  return new Date(lecture.dateOfRecording).getTime();
                }
                return 0;
              };

              const aValue = getDateValue(a);
              const bValue = getDateValue(b);

              if (orderDirection === 'asc') {
                return aValue - bValue;
              } else {
                return bValue - aValue;
              }
            } else {
              // Default sorting for other fields
              const aValue = a[orderByField];
              const bValue = b[orderByField];

              if (orderDirection === 'asc') {
                return aValue > bValue ? 1 : -1;
              } else {
                return aValue < bValue ? 1 : -1;
              }
            }
          });

          // Apply limit
          const limitedLectures = filteredLectures.slice(0, limitCount);

          console.log(`Fetched ${limitedLectures.length} lectures from IndexedDB`);
          return limitedLectures;
        }
      } catch (indexedDBError) {
        console.warn("Error fetching from IndexedDB, falling back to Firebase:", indexedDBError);
        // Continue to Firebase fallback
      }
    }

    // Fallback to Firebase if IndexedDB is disabled or empty
    console.log("Fetching lectures from Firebase");

    // Start building the query constraints
    const queryConstraints: QueryConstraint[] = [];

    // Special handling for dateOfRecording field in Firebase
    if (orderByField === 'dateOfRecording') {
      // For dateOfRecording, we need to order by year, month, and day
      queryConstraints.push(orderBy('dateOfRecording.year', orderDirection));
      queryConstraints.push(orderBy('dateOfRecording.month', orderDirection));
      queryConstraints.push(orderBy('dateOfRecording.day', orderDirection));
    } else {
      // For other fields, use the standard orderBy
      queryConstraints.push(orderBy(orderByField, orderDirection));
    }

    // Add limit constraint
    queryConstraints.push(limit(limitCount));

    // Add language filter if provided
    if (language) {
      // language is a map in the database, check if the "main" key equals the provided language
      queryConstraints.push(where("language.main", "==", language));
    }

    // Add category filter if provided
    if (category) {
      // category is an array in the database, check if it contains the provided category
      queryConstraints.push(where("category", "array-contains", category));
    }

    // Create the query with all constraints
    const q = query(
      collection(db, "lectures"),
      ...queryConstraints
    );

    const querySnapshot = await getDocs(q);

    // Extract data from the documents
    const lectures = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    return lectures;
  } catch (error) {
    console.error("Error fetching lectures: ", error);
    throw new Error("Failed to fetch lectures");
  }
};

export interface searchQueryParams {
  query?: string;
  size?: number;
  from?: number;
  sort?: string;
}
  
export const getDeepSearchResults = async (
  queryParams: searchQueryParams,
): Promise<any> => {
  return fetch({
    url: "/search",
    method: "GET",
    params: queryParams,
  });
};

export const addToFavourite = async (lectureIds: number[]) => {
  try {
    const userId = localStorage.getItem("firebaseUid");
    const addedIds: number[] = [];
    const updatedData = { isFavourite: true };

    if (lectureIds.length > 0) {
      for (const lectureId of lectureIds) {
        const subCollectionRef = collection(db, `users/${userId}/lectureInfo`);

        // Query the sub-collection for a document with the matching `id`
        const q = query(subCollectionRef, where("id", "==", lectureId));
        const querySnapshot = await getDocs(q);

        let documentPath: string;

        if (!querySnapshot.empty) {
          // If a document with the given `id` exists, get its document path
          const docRef = querySnapshot.docs[0];
          documentPath = docRef.ref.path;

          // Update the document
          await setDoc(docRef.ref, { isFavourite: true }, { merge: true });
          addedIds.push(lectureId);
        } else {
          // If no document exists, create a new one
          const newDocRef = doc(subCollectionRef); // Auto-generate a new document ID
          documentPath = newDocRef.path;

          // Add `id` field to updatedData before creating the new document
          await setDoc(newDocRef, { id: lectureId, ...updatedData });
        }

        return documentPath;
      }
    }
  } catch (error) {

  }
}