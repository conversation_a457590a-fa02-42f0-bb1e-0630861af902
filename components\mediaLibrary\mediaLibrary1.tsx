"use client";

import React, { useEffect, useRef, useState, useCallback } from "react";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { useSearchContext } from "@/src/context/search.context";
import { useFilterContext } from "@/src/context/filter.context";
import {
  fetchLectures,
  FetchLecturesPayload,
  getDeepSearchResults,
} from "@/src/api/mediaLibrary.api";
import { useIndexedDBContext } from "@/src/context/indexedDB.context";
import InfiniteScroll from "react-infinite-scroll-component";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

import LectureCard from "../lectureCard/lectureCard";
import LectureCardSkeleton from "../lectureCard/lectureCardSkeleton";
import PlaylistCard from "../playlistCard/playlistCard";
import PlaylistCardSkeleton from "../playlistCard/playlistCardSkeleton";
import PlaybackMode from "../helperComponents/playbackMode";
import SortBy from "../helperComponents/sortBy";
import Filter from "../helperComponents/filter";
import SelectFiles from "../helperComponents/selectFile";
import AddToPlaylist from "../helperComponents/addToPlaylist";
import AddToFavorites from "../helperComponents/addToFavorite";
import MarkAsComplete from "../helperComponents/markAsComplete";
import { CloseOutlined } from "@ant-design/icons";
import ChecklistIcon from "@mui/icons-material/Checklist";

import { Button, message } from "antd";
import { fetchPlaylist } from "@/src/api/playlist.api";
import { mediaLibrarySections, sortByOptions } from "@/src/libs/constant";
import { applyFilters, applySorting } from "@/src/utils/helperFunctions";
import { useAudioContext } from "@/src/context/audio.context";
import { addToFavourite } from "@/src/services/favouriteLecture.service";

// Font configurations
const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const MediaLibrary = () => {
  const { searchQuery, search, deepSearch, isSearching, setIsSearching } =
    useSearchContext();
  const { getAllLectures } = useIndexedDBContext();
  const {
    selectedFilterValues,
    sortBy,
    isFiltering,
    setIsFiltering,
    playbackMode,
  } = useFilterContext();

  const { playAudio } = useAudioContext();

  const [isSelectFileOpen, setIsSelectFileOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<any[]>([]);
  const [allSearchResults, setAllSearchResults] = useState<any[]>([]);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [filteredResults, setFilteredResults] = useState<any[]>([]);
  const [displayResults, setDisplayResults] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isLoading, setIsLoading] = useState(true); // Loading state for initial data fetch
  const [isDeepSearchLoading, setIsDeepSearchLoading] = useState(false); // Loading state for deep search
  const resultsPerPage = 20; // Number of results to load per page
  const [allLectures, setAllLectures] = useState<any[]>([]);

  const [librarySections, setLibrarySections] = useState<any>({
    latestUploads: [],
    latestEnglish: [],
    hindi: [],
    bangla: [],
    bhgavadGita: [],
    shrimadBhagavatam: [],
    caitanyaCaritamrta: [],
    bhajans: [],
    visnuSahasranama: [],
    recommendedPlaylists: [],
  });

  const isFirstRender = useRef(true);
  const isSearchInitialMount = useRef(true);
  const isDeepSearchInitialMount = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      // Fetch library data
      fetchLibrary();
      // Fetch all lectures for filtering and searching
      fetchAllLectures();
    }
  }, []);

  useEffect(() => {
    if (isSearchInitialMount.current) {
      isSearchInitialMount.current = false;
      return;
    }
    if (isSearching) handleSearch();
  }, [search]);

  useEffect(() => {
    if (isDeepSearchInitialMount.current) {
      isDeepSearchInitialMount.current = false;
      return;
    }
    if (isSearching) handleDeepSearch();

    // Clean up function to reset loading state when component unmounts
    return () => {
      setIsDeepSearchLoading(false);
    };
  }, [deepSearch]);

  const fetchLibrary = async () => {
    setIsLoading(true); // Set loading state to true when fetching starts

    const promises = mediaLibrarySections.map(async (section) => {
      try {
        const lectures = await fetchLectures(
          section.payload as FetchLecturesPayload
        );
        return { key: section.key, data: lectures };
      } catch (error) {
        console.error(`Error fetching ${section.key}:`, error);
        return { key: section.key, data: [] };
      }
    });

    const playlistsPromise = fetchPlaylist({
      type: "PublicPlaylists",
      limit: 5,
      orderBy: "creationTime",
      order: "desc",
    });

    try {
      const results = await Promise.all([...promises, playlistsPromise]);
      const updatedSections: any = {};
      results.forEach((result: any, index) => {
        if (index === results.length - 1) {
          updatedSections.recommendedPlaylists = result;
        } else {
          updatedSections[result.key] = result.data;
          console.log("result", result.data);
        }
      });

      setLibrarySections(updatedSections);
    } catch (error) {
      console.error("Error fetching library data:", error);
    } finally {
      setIsLoading(false); // Set loading state to false when fetching completes
    }
  };

  // Process results based on search, filter, and sort
  const processResults = useCallback(
    (initialData: any[], filterValues = selectedFilterValues) => {
      // console.log("Processing results with filter values:", filterValues);

      // Validate input data
      if (!initialData || !Array.isArray(initialData)) {
        console.error("Invalid initial data:", initialData);
        return [];
      }

      // Validate filter values
      if (!filterValues || typeof filterValues !== "object") {
        console.error("Invalid filter values:", filterValues);
        return initialData;
      }

      // First apply filters (including playback mode)
      const filtered = applyFilters(initialData, filterValues, playbackMode);

      // Then apply sorting
      const sorted = applySorting(filtered, sortBy);

      // Update state
      setFilteredResults(sorted);

      // Reset pagination
      setPage(1);

      // Load first page of results
      const firstPageResults = sorted.slice(0, resultsPerPage);
      setDisplayResults(firstPageResults);

      // Set hasMore flag
      setHasMore(sorted.length > resultsPerPage);

      return sorted;
    },
    [sortBy, resultsPerPage, playbackMode]
  );

  // Function to load more results for infinite scroll
  const loadMoreResults = useCallback(() => {
    if (!hasMore || isLoadingMore) return;

    setIsLoadingMore(true);

    setTimeout(() => {
      const nextPage = page + 1;
      const startIndex = (nextPage - 1) * resultsPerPage;
      const endIndex = startIndex + resultsPerPage;

      // Use the already processed filteredResults instead of reprocessing
      console.log(
        "Loading more results from filteredResults:",
        filteredResults.length
      );

      const nextResults = filteredResults.slice(startIndex, endIndex);

      if (nextResults.length > 0) {
        setSearchResults((prev) => [...prev, ...nextResults]);
        setPage(nextPage);
      }

      // Check if we've loaded all results
      if (endIndex >= filteredResults.length) {
        setHasMore(false);
      }

      setIsLoadingMore(false);
    }, 500); // Small delay for better UX
  }, [hasMore, isLoadingMore, page, filteredResults, resultsPerPage]);

  // Reset pagination when search query changes
  useEffect(() => {
    if (isSearching) {
      setPage(1);
      setHasMore(true);
    }
  }, [searchQuery]);

  // Update results when sort option or playback mode changes
  useEffect(() => {
    console.log(
      "Sort option changed to:",
      sortBy,
      "or playback mode changed to:",
      playbackMode
    );

    try {
      if (isSearching) {
        // Apply sorting and filtering to search results
        console.log("Applying sorting and filtering to search results");
        const processed = processResults(
          allSearchResults,
          selectedFilterValues
        );
        setSearchResults(processed.slice(0, resultsPerPage));
        setFilteredResults(processed);
      } else if (
        (isFiltering || playbackMode === 2) &&
        allLectures.length > 0
      ) {
        // Apply sorting and filtering to filtered results
        console.log("Applying sorting and filtering to filtered results");
        const processed = processResults(allLectures, selectedFilterValues);
        setSearchResults(processed.slice(0, resultsPerPage));
        setFilteredResults(processed);
      }
    } catch (error) {
      console.error("Error applying sorting or filtering:", error);
    }
  }, [
    sortBy,
    isSearching,
    isFiltering,
    allSearchResults,
    // allLectures,
    selectedFilterValues,
    processResults,
    resultsPerPage,
    playbackMode,
  ]);

  const handleSearch = async () => {
    resetSelectionState();

    if (!searchQuery.trim()) {
      setAllSearchResults([]);
      setSearchResults([]);
      setHasMore(false);
      setIsSearching(false);
      return;
    }

    try {
      console.log("Searching for:", searchQuery);
      setIsLoadingMore(true);
      setIsSearching(true);

      // Get all lectures from IndexedDB if not already loaded
      let lectures = allLectures;
      if (lectures.length === 0) {
        lectures = await getAllLectures();
        setAllLectures(lectures);
      }

      // Convert search query to lowercase for case-insensitive comparison
      const query = searchQuery.toLowerCase().trim();

      // Split the query into words for full name search
      const queryWords = query
        .split(/\s+/)
        .filter((word: string) => word.length > 0);

      // Filter lectures by comparing search query with title array elements
      const results = lectures.filter((lecture) => {
        // Check if title exists and is an array
        if (!lecture.title || !Array.isArray(lecture.title)) return false;

        // Check for exact match first (highest priority)
        if (
          lecture.title.some(
            (titlePart: string) => titlePart.toLowerCase() === query
          )
        ) {
          return true;
        }

        // Check if any element in the title array contains the full search query
        if (
          lecture.title.some((titlePart: string) =>
            titlePart.toLowerCase().includes(query)
          )
        ) {
          return true;
        }

        // If the query has multiple words, check if all words appear in any title part
        if (queryWords.length > 1) {
          // Join all title parts into a single string for multi-word search
          const fullTitle = lecture.title.join(" ").toLowerCase();

          // Check if all query words are present in the full title
          const allWordsPresent = queryWords.every((word: string) =>
            fullTitle.includes(word)
          );

          if (allWordsPresent) {
            return true;
          }
        }

        return false;
      });

      // Store all results
      console.log("results", results);
      setAllSearchResults(results);

      // Apply any active filters and sorting to the search results
      const processedResults = processResults(results, selectedFilterValues);

      // Reset pagination
      setPage(1);

      // Load first page of results
      const firstPageResults = processedResults.slice(0, resultsPerPage);
      console.log("firstPageResults", firstPageResults);
      console.log(`Applied search with playback mode ${playbackMode}`);
      setSearchResults(firstPageResults);

      // Set hasMore flag
      setHasMore(processedResults.length > resultsPerPage);
    } catch (error) {
      console.error("Error searching lectures:", error);
      setAllSearchResults([]);
      setSearchResults([]);
      setHasMore(false);
    } finally {
      // Add a small delay to make the skeleton loading more noticeable
      setTimeout(() => {
        setIsLoadingMore(false);
      }, 800);
    }
  };

  const handleDeepSearch = async () => {
    if (searchQuery.trim() === "") {
      console.log("1111", 1111);
      setAllSearchResults([]);
      setSearchResults([]);
      setHasMore(false);
      setIsSearching(false);
      return;
    }

    if (isSelectFileOpen) resetSelectionState();

    try {
      console.log("Deep Searching for:", searchQuery);
      setIsLoadingMore(true);
      setIsSearching(true);
      setIsDeepSearchLoading(true); // Set deep search loading state to true
      setSearchResults([]); // Clear previous results to show skeleton

      // Get all lectures from IndexedDB if not already loaded
      const params = {
        query: searchQuery,
        size: 9000,
        from: 0,
      };

      const allLectures = await getAllLectures();
      const response = await getDeepSearchResults(params);

      if (response?.data) {
        // Store all results

        const responseIds =
          response?.data?.transcriptions.map((lecture: any) => lecture.id) ||
          [];

        const requiredLectures = responseIds
          .map((id: any) =>
            allLectures.find((lecture: any) => lecture.id === id)
          )
          .filter((lecture: any) => lecture !== undefined);

        setAllSearchResults(requiredLectures);

        // Apply any active filters and sorting to the search results
        const processedResults = processResults(
          requiredLectures,
          selectedFilterValues
        );

        // Reset pagination
        setPage(1);

        // Load first page of results
        const firstPageResults = processedResults.slice(0, resultsPerPage);
        console.log(`Applied deep search with playback mode ${playbackMode}`);
        setSearchResults(firstPageResults);

        // Set hasMore flag
        setHasMore(processedResults.length > resultsPerPage);
      }
    } catch (error) {
      console.error("Error deep searching lectures:", error);
      setAllSearchResults([]);
      setSearchResults([]);
      setHasMore(false);
    } finally {
      // Add a small delay to make the skeleton loading more noticeable
      setTimeout(() => {
        setIsLoadingMore(false);
        setIsDeepSearchLoading(false); // Set deep search loading state to false
      }, 800);
    }
  };

  // Fetch all lectures for filtering and searching
  const fetchAllLectures = async () => {
    try {
      const lectures = await getAllLectures();
      setAllLectures(lectures);
    } catch (error) {
      console.error("Error fetching all lectures:", error);
    }
  };

  // Handle filter changes
  const handleFilterChange = (values: any) => {
    resetSelectionState();

    // Check if values is a valid object
    if (!values || typeof values !== "object") {
      console.error("Invalid filter values:", values);
      return;
    }

    // Set isFiltering flag if any filters are applied
    const hasActiveFilters = Object.values(values).some(
      (filterValues: any) =>
        Array.isArray(filterValues) && filterValues.length > 0
    );

    setIsFiltering(hasActiveFilters);

    try {
      if (isSearching) {
        // If searching, apply filters to search results
        const filtered = processResults(allSearchResults, values);
        setSearchResults(filtered.slice(0, resultsPerPage));
        setFilteredResults(filtered);
      } else {
        // Otherwise, apply filters to all lectures
        const filtered = processResults(allLectures, values);
        setSearchResults(filtered.slice(0, resultsPerPage));
        setFilteredResults(filtered);
      }
      console.log(`Applied filters with playback mode ${playbackMode}`);
    } catch (error) {
      console.error("Error applying filters:", error);
    }
  };

  const renderSection = (
    title: string,
    data: any[],
    Component: React.FC<any>,
    key: string
  ) => {
    // If loading, show skeleton components
    if (isLoading) {
      return (
        <div className="flex flex-col gap-4" key={key}>
          <h1 className="text-[22px] min-[460px]:text-[24px] font-[600] leading-8">
            {title}
          </h1>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
            {Array(5)
              .fill(0)
              .map((_, index) => (
                <div key={`skeleton-${key}-${index}`}>
                  {key.includes("Playlists") ? (
                    <PlaylistCardSkeleton />
                  ) : (
                    <LectureCardSkeleton />
                  )}
                </div>
              ))}
          </div>
        </div>
      );
    }

    // If no data, return null
    if (!data || data.length === 0) return null;

    // Apply playback mode filter to the section data if it's not a playlist
    let filteredData = data;
    if (!key.includes("Playlists") && playbackMode === 2) {
      // Filter for lectures with videos
      filteredData = data.filter((lecture) => {
        return (
          lecture.resources &&
          lecture.resources.videos &&
          Array.isArray(lecture.resources.videos) &&
          lecture.resources.videos.length > 0
        );
      });

      // If no lectures match the filter, return null
      if (filteredData.length === 0) return null;
    }

    // If data is loaded, show actual components
    return (
      <div className="flex flex-col gap-4" key={key}>
        <h1 className="text-[22px] min-[460px]:text-[24px] font-[600] leading-8">
          {title}
        </h1>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
          {filteredData.map((item: any) => (
            <div key={item.id}>
              <Component
                {...(key.includes("Playlists")
                  ? { playlistData: item }
                  : {
                      lectureData: item,
                      isSelectFileOpen,
                      selectedFiles,
                      setSelectedFiles,
                      playLecture,
                      onLectureUpdated: handleLectureUpdated,
                    })}
              />
            </div>
          ))}
        </div>
      </div>
    );
  };

  const sectionsConfig = [
    { title: "Latest uploads", key: "latestUploads", Component: LectureCard },
    // {
    //   title: "Popular----",
    //   key: "popular",
    //   Component: LectureCard,
    // },
    {
      title: "Latest in English",
      key: "latestEnglish",
      Component: LectureCard,
    },
    { title: "हिन्दी", key: "hindi", Component: LectureCard },
    { title: "বাংলা", key: "bangla", Component: LectureCard },
    { title: "Bhagavad-gītā", key: "bhgavadGita", Component: LectureCard },
    {
      title: "Śrīmad-Bhāgavatam",
      key: "shrimadBhagavatam",
      Component: LectureCard,
    },
    {
      title: "Śrī Caitanya-caritāmṛta",
      key: "caitanyaCaritamrta",
      Component: LectureCard,
    },
    { title: "Bhajans", key: "bhajans", Component: LectureCard },
    {
      title: "Viṣṇu-sahasranāma",
      key: "visnuSahasranama",
      Component: LectureCard,
    },
    {
      title: "Recommended Playlists",
      key: "recommendedPlaylists",
      Component: PlaylistCard,
    },
  ];

  const resetSelectionState = () => {
    setIsSelectFileOpen(false);
    setSelectedFiles([]);
  };

  const playLecture = (lecture: any) => {
    playAudio({
      title: Array.isArray(lecture.title)
        ? lecture.title.join(" ")
        : lecture.title,
      subtitle: Array.isArray(lecture.category)
        ? lecture.category.join(", ")
        : lecture.category,
      audioSrc: lecture.resources?.audios?.[0]?.url || lecture.audioUrl,
      thumbnailUrl: lecture.thumbnail || lecture.thumbnailUrl,
      id: lecture.id, // Include the lecture ID for TopLectureUpdate tracking
    });
  };

  // useEffect(() => {
  //   console.log("selectedFiles", selectedFiles);
  // }, [selectedFiles]);

  // Function to handle favorite updates
  const handleLectureUpdated = async (
    updatedIds: (string | number)[],
    field: string,
    value: boolean
  ) => {
    console.log("updatedIds", updatedIds);
    if (updatedIds.length === 0) return;

    // Fetch updated lectures from IndexedDB
    const updatedLectures = await getAllLectures();
    setAllLectures(updatedLectures);

    // Update library sections with the updated favorite status
    const updatedSections = { ...librarySections };

    // Update each section
    Object.keys(updatedSections).forEach((sectionKey) => {
      if (sectionKey !== "recommendedPlaylists") {
        updatedSections[sectionKey] = updatedSections[sectionKey].map(
          (lecture: any) => {
            if (updatedIds.includes(lecture.id)) {
              return { ...lecture, [field]: value };
            }
            return lecture;
          }
        );
      }
    });

    setLibrarySections(updatedSections);

    // Update search results
    setSearchResults((prevResults) =>
      prevResults.map((lecture: any) => {
        if (updatedIds.includes(lecture.id)) {
          return { ...lecture, [field]: value };
        }
        return lecture;
      })
    );

    // Update filtered results if filtering
    setFilteredResults((prevResults) =>
      prevResults.map((lecture: any) => {
        if (updatedIds.includes(lecture.id)) {
          return { ...lecture, [field]: value };
        }
        return lecture;
      })
    );
  };

  return (
    <div className={`w-full h-full ${poppins.className} tracking-normal`}>
      <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b relative">
        {/* Result and selected count */}
        <div className="hidden md:flex gap-4 md:absolute top-5 min-[460px]:top-2 right-4">
          {isSelectFileOpen && (
            <h2
              className={`text-[12px] leading-5 font-[400] text-text-primary`}
            >
              <span className="font-[500]">Selected:</span>{" "}
              {selectedFiles.length}
            </h2>
          )}
          {(isSearching || isFiltering) && (
            <h2
              className={`text-[12px] leading-5 font-[400] text-text-primary `}
            >
              <span className="font-[500]">Result count:</span>{" "}
              {isFiltering || isSearching
                ? filteredResults.length.toString()
                : allLectures.length.toString()}
            </h2>
          )}
        </div>

        {/* Heading */}
        <h1 className="max-[392px]:text-[24px] max-[460px]:text-[24px] text-[28px] font-[600] leading-8">
          Media Library
        </h1>
        <div className="flex gap-2 items-end">
          {!isSelectFileOpen ? (
            <>
              {(isSearching || isFiltering) && <PlaybackMode />}
              {(isSearching || isFiltering) && <SortBy />}
              <Filter {...{ handleFilterChange }} />
              <SelectFiles {...{ setIsSelectFileOpen }} />
            </>
          ) : (
            <>
              <AddToPlaylist {...{ selectedFiles, setIsSelectFileOpen, setSelectedFiles }} />
              <AddToFavorites
                selectedFiles={selectedFiles}
                onFavoritesUpdated={handleLectureUpdated}
              />
              <MarkAsComplete
                {...{ selectedFiles, onCompleteUpdated: handleLectureUpdated }}
              />
              {/* <Button
                className="h-[32px] flex gap-2 items-center pt-[2px] px-1 md:px-3 text-[13px] text-left border border-[#E0E0E0] hover:!border-[#fd7d14] rounded-[12px] cursor-pointer transition-all"
                onClick={handleSelectAll}
              >
                <h2 className="text-[13.5px] leading-5 font-[400] text-text-primary">
                  <span className="hidden sm:block">
                    {selectedFiles.length > 0 ? "Deselect All" : "Select All"}
                  </span>{" "}
                  <span className="block sm:hidden">
                    <ChecklistIcon />
                  </span>
                </h2>
              </Button> */}
              <div
                className="h-[32px] flex gap-2 items-center pt-[2px] px-2.5 md:px-3 text-[13px] text-left bg-[#E0E0E0] rounded-[12px] hover:opacity-85 cursor-pointer transition-all"
                onClick={resetSelectionState}
              >
                <h2 className="text-[13px] leading-5 font-[400] text-text-primary">
                  <span className="sm:block hidden">Cancel</span>{" "}
                  <span className="block sm:hidden">
                    <CloseOutlined />
                  </span>
                </h2>
              </div>
            </>
          )}
        </div>
      </div>

      <div
        id="scrollableDiv"
        className="w-full h-[calc(100%-60px)] md:h-[calc(100%-84px)] p-5 pb-40 !overflow-x-hidden overflow-y-auto scrollbar"
      >
        {!isSearching && !isFiltering ? (
          <div className="flex flex-col gap-6">
            {sectionsConfig.map(({ title, key, Component }) =>
              renderSection(title, librarySections[key], Component, key)
            )}
          </div>
        ) : (
          <div className="w-full flex flex-col gap-4">
            <div className="flex flex-col gap-1">
              <h1 className="text-[22px] min-[460px]:text-[24px] font-[600] leading-8">
                {/* {isSearching
                  ? `Search Results for "${searchQuery}"`
                  : "Filtered Results"} */}
                {isSearching
                  ?  deepSearch
                    ? `Deep search Results for "${searchQuery}"`
                    : `Search results for "${searchQuery}"`
                  : "Filtered Results"}
              </h1>
              {(() => {
                // Check if any filters are applied
                const hasFilters = Object.values(selectedFilterValues).some(
                  (values: any) => Array.isArray(values) && values.length > 0
                );

                if (hasFilters) {
                  return (
                    <div className="flex flex-wrap items-center gap-2 mb-2">
                      <span className="text-sm text-gray-600">
                        Filters applied:
                      </span>
                      {Object.entries(selectedFilterValues).map(
                        ([filterType, values]: [string, any]) => {
                          if (Array.isArray(values) && values.length > 0) {
                            return (
                              <span
                                key={filterType}
                                className="text-[10px] bg-primary-light text-primary px-2 py-1 rounded-md"
                              >
                                {filterType}: {values.length}
                              </span>
                            );
                          }
                          return null;
                        }
                      )}
                    </div>
                  );
                }
                return null;
              })()}
              {sortBy !== 0 && (
                <div className="text-sm text-gray-600 mb-2">
                  Sorted by:{" "}
                  {sortByOptions.find((option: any) => option.value === sortBy)
                    ?.label || "Default view"}
                </div>
              )}
            </div>
            {isDeepSearchLoading ? (
              // Show skeleton loading during deep search
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                {Array(15)
                  .fill(0)
                  .map((_, index) => (
                    <LectureCardSkeleton
                      key={`deep-search-skeleton-${index}`}
                    />
                  ))}
              </div>
            ) : searchResults.length > 0 ? (
              <InfiniteScroll
                dataLength={searchResults.length}
                next={loadMoreResults}
                hasMore={hasMore}
                loader={
                  <div className="w-full grid grid-cols-5 gap-5 mt-4">
                    {Array(5)
                      .fill(0)
                      .map((_, index) => (
                        <LectureCardSkeleton key={`search-skeleton-${index}`} />
                      ))}
                  </div>
                }
                endMessage={null}
                scrollableTarget="scrollableDiv"
                scrollThreshold={0.95}
                className="!overflow-x-hidden"
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                  {searchResults.map((lecture: any) => (
                    <LectureCard
                      key={lecture.id}
                      lectureData={lecture}
                      {...{
                        isSelectFileOpen,
                        selectedFiles,
                        setSelectedFiles,
                        playLecture,
                        onLectureUpdated: handleLectureUpdated,
                      }}
                    />
                  ))}
                </div>
              </InfiniteScroll>
            ) : (
              <div className="text-center py-10">
                <p className="text-lg text-gray-500">
                  {isSearching
                    ? `No results found for "${searchQuery}"${
                        isFiltering ? " with the applied filters" : ""
                      }`
                    : "No results match the applied filters"}
                </p>
                <p className="text-sm text-gray-400 mt-2">
                  {isSearching
                    ? "Try different keywords or check your spelling"
                    : "Try adjusting your filter criteria"}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MediaLibrary;
