"use client";
import React, { createContext, useContext, useState } from "react";

const SearchContext = createContext();

export const SearchContextProvider = ({ children }) => {
    const [searchQuery, setSearchQuery] = useState("");
    const [search, setSearch] = useState(false);
    const [deepSearch, setDeepSearch] = useState(false);
    const [isSearching, setIsSearching] = useState(false);
    const [isDeepSearching, setIsDeepSearching] = useState(false)

    const resetSearchStates = () => {
        setSearch("");
        setSearchQuery("");
        setSearch(false);
        setDeepSearch(false);
        setIsSearching(false);
        setIsDeepSearching(false)
    };

    return (
        <SearchContext.Provider
            value={{
                searchQuery,
                setSearchQuery,
                search,
                setSearch,
                deepSearch,
                setDeepSearch,
                isSearching,
                setIsSearching,
                resetSearchStates,
                isDeepSearching, setIsDeepSearching
            }}
        >
            {children}
        </SearchContext.Provider>
    );
};

export const useSearchContext = () => {
    return useContext(SearchContext);
};
