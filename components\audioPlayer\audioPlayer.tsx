"use client";

import React, { useRef, useState, useEffect } from "react";
import { Poppins } from "next/font/google";
import { useAudioContext } from "@/src/context/audio.context";
import { Tooltip, Dropdown, Image } from "antd";
import CustomSlider from "../ui/customSlider";
import { useRouter, usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import {
    FaPlay,
    FaPause,
    FaVolumeUp,
    FaVolumeDown,
    FaVolumeMute,
    FaStepForward,
    FaStepBackward,
    FaHeart,
    FaRegHeart,
    FaShareAlt,
    FaEllipsisV,
} from "react-icons/fa";
import { RiReplay10Fill, RiForward10Fill } from "react-icons/ri";
import { IoClose } from "react-icons/io5";
import {
    MdOutlineRepeat,
    MdOutlineRepeatOn,
    MdOutlineRepeatOne,
} from "react-icons/md";
import { CgTranscript } from "react-icons/cg";
import BVKSConfig from "@/src/config/apps/bvks";
import ShareModal from "../Modal/shareModal";
import AddToPlaylist from "../Modal/addToPlaylistModal";
import { useSearchContext } from "@/src/context/search.context";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface HighlightedSection {
    timeInSeconds: number;
    text: string;
    timestamp: string;
}

const formatTime = (time: number) => {
    if (isNaN(time)) return "0:00";

    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);

    if (hours > 0) {
        return `${hours}:${minutes < 10 ? "0" : ""}${minutes}:${
            seconds < 10 ? "0" : ""
        }${seconds}`;
    } else {
        return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
    }
};

const AudioPlayer = () => {
    const router = useRouter();
    const pathname = usePathname();
    const [repeatMode, setRepeatMode] = useState<"none" | "all" | "one">(
        "none"
    );
    const [showVolumeSlider, setShowVolumeSlider] = useState(false);
    const [isAddToPlaylistModalOpen, setIsAddToPlaylistModalOpen] =
        useState(false);
    const [speedOptions] = useState([0.5, 0.75, 1, 1.25, 1.5, 1.75, 2]);
    const [showSpeedOptions, setShowSpeedOptions] = useState(false);
    const [hoveredHighlight, setHoveredHighlight] = useState<{
        text: string;
        timestamp: string;
        position: { x: number; y: number };
    } | null>(null);
    const [hoveredTime, setHoveredTime] = useState<number | null>(null);
    const [mousePosition, setMousePosition] = useState<{
        x: number;
        y: number;
    }>({ x: 0, y: 0 });
    const [hoverPosition, setHoverPosition] = useState<number | null>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [isHoveringCircle, setIsHoveringCircle] = useState(false);
    const volumeControlRef = useRef<HTMLDivElement>(null);

    const {
        isPlaying,
        setIsPlaying,
        currentTime,
        duration,
        volume,
        setVolume,
        playbackRate,
        setPlaybackRate,
        showPlayer,
        currentAudio,
        togglePlay,
        closePlayer,
        seekTo,
        isFavorite,
        isProcessing,
        toggleFavorite,
        isShareModalOpen,
        setIsShareModalOpen,
        audioRef,
        highlightedSections,
    } = useAudioContext();

    const { searchQuery, deepSearch } = useSearchContext();

    const progressBarRef = useRef<HTMLDivElement>(null);

    // Check if current page is a transcription page
    const isTranscriptionPage =
        pathname?.includes("/knowledge-base/transcription/") ||
        pathname?.includes("/transcription/");

    // Handle audio end for repeat functionality
    useEffect(() => {
        const handleAudioEnd = () => {
            if (repeatMode === "one") {
                if (audioRef.current) {
                    audioRef.current.currentTime = 0;
                    setTimeout(() => {
                        if (audioRef.current) {
                            audioRef.current.play();
                            setIsPlaying(true);
                        }
                    }, 50);
                }
            } else if (repeatMode === "all") {
                if (audioRef.current) {
                    audioRef.current.currentTime = 0;
                    setTimeout(() => {
                        if (audioRef.current) {
                            audioRef.current.play();
                            setIsPlaying(true);
                        }
                    }, 50);
                }
            }
        };

        if (audioRef.current) {
            audioRef.current.addEventListener("ended", handleAudioEnd);
        }

        return () => {
            if (audioRef.current) {
                audioRef.current.removeEventListener("ended", handleAudioEnd);
            }
        };
    }, [repeatMode, audioRef, setIsPlaying]);

    // Handle clicks outside the volume control
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                showVolumeSlider &&
                volumeControlRef.current &&
                !volumeControlRef.current.contains(event.target as Node)
            ) {
                setShowVolumeSlider(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [showVolumeSlider]);

    const handleProgressBarClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (progressBarRef.current) {
            const rect = progressBarRef.current.getBoundingClientRect();
            const clickPosition = e.clientX - rect.left;
            const progressBarWidth = rect.width;
            const clickPercentage = clickPosition / progressBarWidth;
            const newTime = clickPercentage * duration;
            seekTo(newTime);
        }
    };

    const handleProgressBarMouseMove = (
        e: React.MouseEvent<HTMLDivElement>
    ) => {
        if (progressBarRef.current && duration > 0) {
            const rect = progressBarRef.current.getBoundingClientRect();
            const hoverPositionX = e.clientX - rect.left;
            const progressBarWidth = rect.width;
            const hoverPercentage = Math.max(
                0,
                Math.min(1, hoverPositionX / progressBarWidth)
            );
            const hoverTime = hoverPercentage * duration;

            setHoveredTime(hoverTime);
            setMousePosition({ x: e.clientX, y: e.clientY });
            setHoverPosition(hoverPercentage * 100); // Store as percentage for positioning
        }
    };

    const handleProgressBarMouseLeave = () => {
        setHoveredTime(null);
        setHoverPosition(null);
    };

    const handleCircleMouseDown = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleCircleMouseEnter = () => {
        setIsHoveringCircle(true);
    };

    const handleCircleMouseLeave = () => {
        setIsHoveringCircle(false);
    };

    // Handle global mouse events for dragging
    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => {
            if (isDragging && progressBarRef.current && duration > 0) {
                const rect = progressBarRef.current.getBoundingClientRect();
                const dragPosition = e.clientX - rect.left;
                const progressBarWidth = rect.width;
                const dragPercentage = Math.max(
                    0,
                    Math.min(1, dragPosition / progressBarWidth)
                );
                const newTime = dragPercentage * duration;
                seekTo(newTime);
            }
        };

        const handleMouseUp = () => {
            if (isDragging) {
                setIsDragging(false);
            }
        };

        if (isDragging) {
            document.addEventListener("mousemove", handleMouseMove);
            document.addEventListener("mouseup", handleMouseUp);
        }

        return () => {
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
        };
    }, [isDragging, duration, seekTo]);

    const getVolumeIcon = () => {
        if (volume === 0) return <FaVolumeMute />;
        if (volume < 0.5) return <FaVolumeDown />;
        return <FaVolumeUp />;
    };

    const handleShare = () => {
        setIsShareModalOpen(true);
    };

    const toggleRepeatMode = () => {
        if (repeatMode === "none") setRepeatMode("all");
        else if (repeatMode === "all") setRepeatMode("one");
        else setRepeatMode("none");
    };

    const handleVolumeClick = () => {
        if (volume > 0) {
            setVolume(0);
        } else {
            setVolume(100);
        }
    };

    const toggleVolumeSlider = () => {
        setShowVolumeSlider((prev) => !prev);
    };

    const handleVolumeChange = (value: number) => {
        // Convert from 0-100 to 0-1 for audio element
        setVolume(value / 100);
    };

    const handleRewind10 = () => {
        if (audioRef.current) {
            const newTime = Math.max(0, currentTime - 10);
            seekTo(newTime);
        }
    };

    const handleForward10 = () => {
        if (audioRef.current) {
            const newTime = Math.min(duration, currentTime + 10);
            seekTo(newTime);
        }
    };

    const navigateToTranscription = () => {
        if (currentAudio.id) {
            if (deepSearch && searchQuery) {
                router.push(
                    `/knowledge-base/transcription/${currentAudio.id}?search=${encodeURIComponent(
                        searchQuery
                    )}&prev=medialib`
                );
            } else {
                router.push(`/knowledge-base/transcription/${currentAudio.id}`);
            }
        }
    };

    const handleAddToPlaylist = (e: any) => {
        if (e && e.domEvent) {
            e.domEvent.stopPropagation();
        }
        setIsAddToPlaylistModalOpen(true);
    };

    const getRepeatIcon = () => {
        switch (repeatMode) {
            case "all":
                return <MdOutlineRepeatOn size={18} />;
            case "one":
                return <MdOutlineRepeatOne size={18} />;
            default:
                return <MdOutlineRepeat size={18} />;
        }
    };

    const handleSpeedChange = (speed: number) => {
        setPlaybackRate(speed);
        setShowSpeedOptions(false);
    };

    // Add animation variants
    const playerVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                type: "spring",
                stiffness: 300,
                damping: 25,
            },
        },
        exit: {
            opacity: 0,
            y: 20,
            transition: {
                duration: 0.2,
            },
        },
    };

    return (
        <AnimatePresence>
            {showPlayer && (
                <motion.div
                    className={`fixed bottom-0 left-0 right-0 bg-white shadow-[0_-2px_10px_rgba(0,0,0,0.1)] z-50 h-auto xl:h-[70px] ${poppins.className}`}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    variants={playerVariants}
                >
                    <div className="flex flex-col space-y-3 xl:space-y-0 xl:flex-row items-center px-4 xl:px-20 py-2 xl:py-1 border-t border-[#E0E0E0] h-full">
                        {/* Progress bar section - First on mobile, third on desktop */}
                        <div className="w-full order-1 xl:order-3 xl:w-auto xl:flex-grow flex items-center mb-2 xl:mb-0">
                            <div className="flex items-center gap-2 w-full">
                                <span className="text-xs text-[#343A40] opacity-70 min-w-[36px] text-right">
                                    {formatTime(currentTime)}
                                </span>
                                <div
                                    ref={progressBarRef}
                                    className={`w-full bg-[#E0E0E0] rounded-full cursor-pointer relative xl:min-w-[350px] transition-all duration-200 ${
                                        hoveredTime !== null ? "h-2" : "h-1"
                                    }`}
                                    onClick={handleProgressBarClick}
                                    onMouseMove={handleProgressBarMouseMove}
                                    onMouseLeave={handleProgressBarMouseLeave}
                                >
                                    <div
                                        className="absolute top-0 left-0 h-full bg-primary rounded-full"
                                        style={{
                                            width:
                                                isNaN(duration) ||
                                                duration === 0
                                                    ? "0%"
                                                    : `${
                                                          (currentTime /
                                                              duration) *
                                                          100
                                                      }%`,
                                        }}
                                    ></div>

                                    {/* Current position indicator circle */}
                                    {duration > 0 && (
                                        <div
                                            className={`absolute top-1/2 bg-primary rounded-full transform -translate-y-1/2 -translate-x-1/2 shadow-sm border-2 border-primary cursor-pointer transition-all duration-200 ${
                                                isHoveringCircle ||
                                                isDragging ||
                                                hoveredTime !== null
                                                    ? "w-4 h-4"
                                                    : "w-2 h-2"
                                            }`}
                                            style={{
                                                left: `${
                                                    isNaN(duration) ||
                                                    duration === 0
                                                        ? "0%"
                                                        : `${
                                                              (currentTime /
                                                                  duration) *
                                                              100
                                                          }%`
                                                }`,
                                            }}
                                            onMouseDown={handleCircleMouseDown}
                                            onMouseEnter={
                                                handleCircleMouseEnter
                                            }
                                            onMouseLeave={
                                                handleCircleMouseLeave
                                            }
                                        ></div>
                                    )}

                                    {/* Hover position indicator line */}
                                    {hoverPosition !== null && (
                                        <div
                                            className="absolute top-0 w-0.5 h-full bg-gray-700 opacity-80 pointer-events-none"
                                            style={{
                                                left: `${hoverPosition}%`,
                                            }}
                                        ></div>
                                    )}
                                    {/* Blue markers for highlighted sections */}
                                    {highlightedSections &&
                                        highlightedSections.length > 0 &&
                                        duration > 0 &&
                                        (
                                            highlightedSections as HighlightedSection[]
                                        ).map(
                                            (
                                                section: HighlightedSection,
                                                index: number
                                            ) => {
                                                const position =
                                                    (section.timeInSeconds /
                                                        duration) *
                                                    100;
                                                return (
                                                    <div
                                                        key={index}
                                                        className="absolute top-0 w-1 h-full bg-blue-600 cursor-pointer hover:bg-blue-800 transition-colors"
                                                        style={{
                                                            left: `${position}%`,
                                                        }}
                                                        onMouseEnter={(e) =>
                                                            setHoveredHighlight(
                                                                {
                                                                    text: section.text,
                                                                    timestamp:
                                                                        section.timestamp,
                                                                    position: {
                                                                        x: e.clientX,
                                                                        y: e.clientY,
                                                                    },
                                                                }
                                                            )
                                                        }
                                                        onMouseLeave={() =>
                                                            setHoveredHighlight(
                                                                null
                                                            )
                                                        }
                                                        onClick={() =>
                                                            seekTo &&
                                                            seekTo(
                                                                section.timeInSeconds
                                                            )
                                                        }
                                                        // title={`${section.timestamp}: ${section.text}`}
                                                    ></div>
                                                );
                                            }
                                        )}

                                    {/* Hover tooltip for highlighted sections */}
                                    {hoveredHighlight && (
                                        <div
                                            className="fixed bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-50 pointer-events-none"
                                            style={{
                                                left: `${hoveredHighlight.position.x}px`,
                                                top: `${
                                                    hoveredHighlight.position
                                                        .y - 50
                                                }px`,
                                                transform: "translateX(-50%)",
                                            }}
                                        >
                                            <div className="font-medium">
                                                {hoveredHighlight.timestamp}
                                            </div>
                                            <div className="max-w-[200px] truncate">
                                                {hoveredHighlight.text}
                                            </div>
                                        </div>
                                    )}

                                    {/* General hover tooltip for time */}
                                    {hoveredTime !== null &&
                                        !hoveredHighlight && (
                                            <div
                                                className="fixed bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-50 pointer-events-none"
                                                style={{
                                                    left: `${mousePosition.x}px`,
                                                    top: `${
                                                        mousePosition.y - 35
                                                    }px`,
                                                    transform:
                                                        "translateX(-50%)",
                                                }}
                                            >
                                                {formatTime(hoveredTime)}
                                            </div>
                                        )}
                                </div>
                                <span className="text-xs text-[#343A40] opacity-70 min-w-[40px]">
                                    -{formatTime(duration - currentTime)}
                                </span>
                            </div>
                        </div>

                        {/* Left section - Lecture info and thumbnail - Second on mobile, first on desktop */}
                        <div className="flex items-center gap-2 flex-shrink-0 w-full xl:w-[200px] order-2 xl:order-1">
                            <div className="relative min-w-[80px] h-[45px]">
                                <Image
                                    src={
                                        currentAudio.thumbnailUrl ||
                                        BVKSConfig.defaultLectureThumbnail
                                    }
                                    width={80}
                                    height={45}
                                    alt={currentAudio.title}
                                    className="rounded w-[80px] h-[45px]"
                                />
                            </div>
                            <div className="truncate">
                                <Tooltip
                                    title={
                                        <span className={poppins.className}>
                                            {currentAudio.title ||
                                                "Lecture on Initiation"}
                                        </span>
                                    }
                                    placement="top"
                                >
                                    <h3 className="text-sm font-medium text-[#343A40] marquee">
                                        <span className="marquee-content">
                                            {currentAudio.title ||
                                                "Lecture on Initiation"}
                                        </span>
                                    </h3>
                                </Tooltip>
                                <Tooltip
                                    title={
                                        <span className={poppins.className}>
                                            {currentAudio.subtitle ||
                                                "Bhagavad-gītā"}
                                        </span>
                                    }
                                    placement="top"
                                >
                                    <p className="text-xs text-[#343A40] opacity-70 marquee">
                                        <span className="marquee-content">
                                            {currentAudio.subtitle ||
                                                "Bhagavad-gītā"}
                                        </span>
                                    </p>
                                </Tooltip>
                            </div>
                        </div>

                        {/* Control buttons section - Third on mobile (in row), second on desktop */}
                        <div className="w-full xl:w-auto flex flex-row items-center justify-between xl:justify-start gap-2 order-3 xl:order-2">
                            {/* Play controls */}
                            <div className="flex items-center justify-center gap-3 xl:mx-3">
                                {/* Repeat button */}
                                <button
                                    type="button"
                                    className={`text-[#343A40] ${
                                        repeatMode !== "none"
                                            ? "text-primary"
                                            : "opacity-70"
                                    } hover:opacity-100`}
                                    onClick={toggleRepeatMode}
                                    aria-label={`Repeat mode: ${repeatMode}`}
                                    title={`Repeat mode: ${repeatMode}`}
                                >
                                    {getRepeatIcon()}
                                </button>

                                {/* Transcript button - only show if not on transcription page */}
                                {!isTranscriptionPage && currentAudio.id && (
                                    <button
                                        type="button"
                                        className="text-[#343A40] opacity-70 hover:opacity-100"
                                        onClick={navigateToTranscription}
                                        aria-label="View transcription"
                                        title="View transcription"
                                    >
                                        <CgTranscript size={18} />
                                    </button>
                                )}

                                {/* Previous track button */}
                                <button
                                    type="button"
                                    className="text-[#343A40] opacity-70 hover:opacity-100"
                                    aria-label="Previous track"
                                    title="Previous track"
                                >
                                    <FaStepBackward size={16} />
                                </button>

                                {/* Rewind 10s button */}
                                <button
                                    type="button"
                                    className="text-[#343A40] opacity-70 hover:opacity-100"
                                    onClick={handleRewind10}
                                    aria-label="Rewind 10 seconds"
                                    title="Rewind 10 seconds"
                                >
                                    <RiReplay10Fill size={20} />
                                </button>

                                {/* Play/Pause button */}
                                <button
                                    type="button"
                                    className="text-[#343A40] hover:text-primary transition-colors"
                                    onClick={togglePlay}
                                    aria-label={isPlaying ? "Pause" : "Play"}
                                    title={isPlaying ? "Pause" : "Play"}
                                >
                                    {isPlaying ? (
                                        <FaPause size={18} />
                                    ) : (
                                        <FaPlay size={18} />
                                    )}
                                </button>

                                {/* Forward 10s button */}
                                <button
                                    type="button"
                                    className="text-[#343A40] opacity-70 hover:opacity-100"
                                    onClick={handleForward10}
                                    aria-label="Forward 10 seconds"
                                    title="Forward 10 seconds"
                                >
                                    <RiForward10Fill size={20} />
                                </button>

                                {/* Next track button */}
                                <button
                                    type="button"
                                    className="text-[#343A40] opacity-70 hover:opacity-100"
                                    aria-label="Next track"
                                    title="Next track"
                                >
                                    <FaStepForward size={16} />
                                </button>

                                {/* Volume control */}
                                <div
                                    ref={volumeControlRef}
                                    className="relative flex justify-center items-center"
                                >
                                    <button
                                        type="button"
                                        className="text-[#343A40] opacity-70 hover:opacity-100"
                                        onClick={toggleVolumeSlider}
                                        aria-label="Volume control"
                                        title="Volume control"
                                    >
                                        {getVolumeIcon()}
                                    </button>

                                    {showVolumeSlider && (
                                        <div
                                            className="absolute bottom-[25px] left-1/2 transform -translate-x-1/2 bg-white shadow-md rounded-md p-3 w-[40px] h-[120px] flex flex-col items-center justify-center z-50"
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            <CustomSlider
                                                vertical
                                                min={0}
                                                max={100}
                                                value={Math.round(volume * 100)}
                                                onChange={handleVolumeChange}
                                                className="h-[100px] w-full"
                                            />
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Right controls */}
                            <div className="flex items-center justify-center gap-3 xl:hidden">
                                {/* Playback speed selector */}
                                <div className="relative">
                                    <button
                                        type="button"
                                        className="text-sm font-medium text-[#343A40] hover:text-primary"
                                        onClick={() =>
                                            setShowSpeedOptions(
                                                !showSpeedOptions
                                            )
                                        }
                                    >
                                        {playbackRate}x
                                    </button>

                                    {showSpeedOptions && (
                                        <div className="absolute bottom-8 right-0 bg-white shadow-md rounded-md p-2 w-[80px] z-50">
                                            {speedOptions.map((speed) => (
                                                <div
                                                    key={speed}
                                                    className={`py-1 px-2 cursor-pointer hover:bg-gray-100 text-sm ${
                                                        playbackRate === speed
                                                            ? "font-bold text-primary"
                                                            : ""
                                                    }`}
                                                    onClick={() =>
                                                        handleSpeedChange(speed)
                                                    }
                                                >
                                                    {speed}x
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                {/* Action buttons */}
                                <div className="flex items-center gap-3">
                                    {/* Favorite button */}
                                    <button
                                        type="button"
                                        className={`${
                                            isFavorite ? "" : "text-primary"
                                        } hover:opacity-80`}
                                        aria-label={
                                            isFavorite
                                                ? "Remove from favorites"
                                                : "Add to favorites"
                                        }
                                        title={
                                            isFavorite
                                                ? "Remove from favorites"
                                                : "Add to favorites"
                                        }
                                        onClick={toggleFavorite}
                                        disabled={
                                            isProcessing || !currentAudio.id
                                        }
                                    >
                                        {isFavorite ? (
                                            <FaHeart size={16} />
                                        ) : (
                                            <FaRegHeart size={16} />
                                        )}
                                    </button>

                                    {/* Share button */}
                                    {/* <button
                                        type="button"
                                        onClick={handleShare}
                                        className="text-[#343A40] opacity-70 hover:opacity-100"
                                        aria-label="Share"
                                        title="Share"
                                    >
                                        <FaShareAlt size={16} />
                                    </button> */}

                                    {/* More options dropdown */}
                                    <Dropdown
                                        menu={{
                                            items: [
                                                {
                                                    key: "1",
                                                    label: "Add to Playlist",
                                                    onClick:
                                                        handleAddToPlaylist,
                                                },
                                            ],
                                        }}
                                        trigger={["click"]}
                                        placement="topRight"
                                        destroyPopupOnHide={true}
                                    >
                                        <button
                                            type="button"
                                            className="text-[#343A40] opacity-70 hover:opacity-100"
                                            aria-label="More options"
                                            title="More options"
                                            onClick={(e) => e.preventDefault()}
                                        >
                                            <FaEllipsisV size={16} />
                                        </button>
                                    </Dropdown>

                                    {/* Close button */}
                                    <button
                                        type="button"
                                        className="text-[#343A40] opacity-70 hover:opacity-100 ml-1"
                                        onClick={closePlayer}
                                        aria-label="Close audio player"
                                        title="Close audio player"
                                    >
                                        <IoClose size={20} />
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Right controls */}
                        <div className="xl:flex items-center justify-center gap-3 hidden order-4 mx-3">
                            {/* Playback speed selector */}
                            <div className="relative">
                                <button
                                    type="button"
                                    className="text-sm font-medium text-[#343A40] hover:text-primary"
                                    onClick={() =>
                                        setShowSpeedOptions(!showSpeedOptions)
                                    }
                                >
                                    {playbackRate}x
                                </button>

                                {showSpeedOptions && (
                                    <div className="absolute bottom-8 right-0 bg-white shadow-md rounded-md p-2 w-[80px] z-50">
                                        {speedOptions.map((speed) => (
                                            <div
                                                key={speed}
                                                className={`py-1 px-2 cursor-pointer hover:bg-gray-100 text-sm ${
                                                    playbackRate === speed
                                                        ? "font-bold text-primary"
                                                        : ""
                                                }`}
                                                onClick={() =>
                                                    handleSpeedChange(speed)
                                                }
                                            >
                                                {speed}x
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* Action buttons */}
                            <div className="flex items-center gap-3">
                                {/* Favorite button */}
                                <button
                                    type="button"
                                    className={`hover:opacity-80 text-primary`}
                                    aria-label={
                                        isFavorite
                                            ? "Remove from favorites"
                                            : "Add to favorites"
                                    }
                                    title={
                                        isFavorite
                                            ? "Remove from favorites"
                                            : "Add to favorites"
                                    }
                                    onClick={toggleFavorite}
                                    disabled={isProcessing || !currentAudio.id}
                                >
                                    {isFavorite ? (
                                        <FaHeart size={16} />
                                    ) : (
                                        <FaRegHeart size={16} />
                                    )}
                                </button>

                                {/* Share button */}
                                {/* <button
                                    type="button"
                                    onClick={handleShare}
                                    className="text-[#343A40] opacity-70 hover:opacity-100"
                                    aria-label="Share"
                                    title="Share"
                                >
                                    <FaShareAlt size={16} />
                                </button> */}

                                {/* More options dropdown */}
                                <Dropdown
                                    menu={{
                                        items: [
                                            {
                                                key: "1",
                                                label: "Add to Playlist",
                                                onClick: handleAddToPlaylist,
                                            },
                                        ],
                                    }}
                                    trigger={["click"]}
                                    placement="topRight"
                                    destroyPopupOnHide={true}
                                >
                                    <button
                                        type="button"
                                        className="text-[#343A40] opacity-70 hover:opacity-100"
                                        aria-label="More options"
                                        title="More options"
                                        onClick={(e) => e.preventDefault()}
                                    >
                                        <FaEllipsisV size={16} />
                                    </button>
                                </Dropdown>

                                {/* Close button */}
                                <button
                                    type="button"
                                    className="text-[#343A40] opacity-70 hover:opacity-100 ml-1"
                                    onClick={closePlayer}
                                    aria-label="Close audio player"
                                    title="Close audio player"
                                >
                                    <IoClose size={20} />
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Share Modal */}
                    <ShareModal
                        isModalOpen={isShareModalOpen}
                        setIsModalOpen={setIsShareModalOpen}
                        title={currentAudio.title}
                        url={window.location.href}
                    />

                    {/* Add to Playlist Modal */}
                    <AddToPlaylist
                        isModalOpen={isAddToPlaylistModalOpen}
                        setIsModalOpen={setIsAddToPlaylistModalOpen}
                        selectedFiles={currentAudio}
                    />
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default AudioPlayer;
