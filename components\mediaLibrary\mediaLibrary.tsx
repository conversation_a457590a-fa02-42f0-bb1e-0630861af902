"use client";

import React, { useEffect, useRef, useState, useCallback } from "react";
import { Pop<PERSON>s } from "next/font/google";
import { useSearchContext } from "@/src/context/search.context";
import { useFilterContext } from "@/src/context/filter.context";
import InfiniteScroll from "react-infinite-scroll-component";
import { CloseOutlined } from "@ant-design/icons";
import { message } from "antd";

import LectureCard from "../lectureCard/lectureCard";
import LectureCardSkeleton from "../lectureCard/lectureCardSkeleton";
import PlaylistCard from "../playlistCard/playlistCard";
import PlaylistCardSkeleton from "../playlistCard/playlistCardSkeleton";
import PlaybackMode from "../helperComponents/playbackMode";
import SortBy from "../helperComponents/sortBy";
import Filter from "../helperComponents/filter";
import SelectFiles from "../helperComponents/selectFile";
import AddToPlaylist from "../helperComponents/addToPlaylist";
import AddToFavorites from "../helperComponents/addToFavorite";
import RemoveFavorites from "../helperComponents/removeFavourite";
import ResetAsComplete from "../helperComponents/resetAsComplete";
import MarkAsComplete from "../helperComponents/markAsComplete";

import { sortByOptions } from "@/src/libs/constant";
import {
  fetchLibrarySections,
  fetchMediaLibrarySearch,
  fetchAllMediaLibraryLectures,
  fetchDeepSearchResults,
} from "@/src/services/mediaLibrary.service";
import AudioToggleSwitch from "../helperComponents/audioToggleSwitch";
import { useAudioContext } from "@/src/context/audio.context";

// Font configurations
const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const MediaLibrary = () => {
  const {
    searchQuery,
    search,
    deepSearch,
    isSearching,
    setIsSearching,
    isDeepSearching,
  } = useSearchContext();
  const {
    selectedFilterValues,
    sortBy,
    isFiltering,
    setIsFiltering,
    playbackMode,
    resetFilters,
  } = useFilterContext();

  const [isSelectFileOpen, setIsSelectFileOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<any[]>([]);
  const [allLectures, setAllLectures] = useState<any[]>([]);
  const [displayedLectures, setDisplayedLectures] = useState<any[]>([]);
  const [isDeepSearchLoading, setIsDeepSearchLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [libraryLoading, setLibraryLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const resultsPerPage = 20;

  const [librarySections, setLibrarySections] = useState<any>({
    latestUploads: [],
    latestEnglish: [],
    hindi: [],
    bangla: [],
    bhgavadGita: [],
    shrimadBhagavatam: [],
    caitanyaCaritamrta: [],
    bhajans: [],
    visnuSahasranama: [],
    recommendedPlaylists: [],
  });

  const isFirstRender = useRef(true);
  const isSearchInitialMount = useRef(true);
  const isDeepSearchInitialMount = useRef(true);

  // Load initial data
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      loadLibraryData();
      loadAllLectures();
    }
  }, []);

  // Handle search
  useEffect(() => {
    if (isSearchInitialMount.current) {
      isSearchInitialMount.current = false;
      return;
    }
    handleSearch();
  }, [search]);

  // Handle deep search
  useEffect(() => {
    if (isDeepSearchInitialMount.current) {
      isDeepSearchInitialMount.current = false;
      return;
    }
    handleDeepSearch(0, null);

    return () => {
      setIsDeepSearchLoading(false);
    };
  }, [deepSearch]);

  // Handle filter/sort changes
  useEffect(() => {
    if (!isFirstRender.current) {
      if (isSearching) {
        // Re-apply current search with new filters/sort
        if (isDeepSearching) {
          handleDeepSearch(sortBy, selectedFilterValues);
        } else {
          handleSearch();
        }
      } else if (isFiltering || playbackMode !== 1) {
        // Apply filters to all lectures
        handleFiltering();
      } else {
        // Reload library sections with new playback mode
        loadLibraryData();
      }
    }
  }, [sortBy, selectedFilterValues, playbackMode]);

  const loadLibraryData = async () => {
    try {
      setLibraryLoading(true);

      const sections = await fetchLibrarySections({
        playbackMode,
      });

      console.log("sections========>", sections);

      setLibrarySections(sections);
    } catch (error) {
      console.error("Error loading library:", error);
      message.error(
        "We couldn't fetch library at this moment, please try again later."
      );
    } finally {
      setLibraryLoading(false);
    }
  };

  const loadAllLectures = async () => {
    try {
      const lectures = await fetchAllMediaLibraryLectures();
      setAllLectures(lectures);
    } catch (error) {
      console.error("Error loading all lectures:", error);
    }
  };

  const handleFiltering = async () => {
    try {
      setLoading(true);

      const lectures = await fetchMediaLibrarySearch({
        searchQuery: "",
        sortBy,
        allFilter: selectedFilterValues,
        playbackMode,
      });

      setAllLectures(lectures);

      // Reset pagination and load first page
      setPage(1);
      const firstPageLectures = lectures.slice(0, resultsPerPage);
      setDisplayedLectures(firstPageLectures);
      setHasMore(lectures.length > resultsPerPage);
    } catch (error) {
      console.error("Error filtering lectures:", error);
      setAllLectures([]);
      setDisplayedLectures([]);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  };

  const resetSelectionState = () => {
    setIsSelectFileOpen(false);
    setSelectedFiles([]);
  };

  const handleSearch = async () => {
    resetSelectionState();

    if (!searchQuery.trim()) {
      setIsSearching(false);
      return;
    }

    try {
      setIsLoadingMore(true);
      setIsSearching(true);

      const lectures = await fetchMediaLibrarySearch({
        searchQuery,
        sortBy,
        allFilter: selectedFilterValues,
        playbackMode,
      });

      setAllLectures(lectures);

      // Reset pagination and load first page
      setPage(1);
      const firstPageLectures = lectures.slice(0, resultsPerPage);
      setDisplayedLectures(firstPageLectures);
      setHasMore(lectures.length > resultsPerPage);
    } catch (error) {
      console.error("Error searching lectures:", error);
      setAllLectures([]);
      setDisplayedLectures([]);
      setHasMore(false);
    } finally {
      setTimeout(() => {
        setIsLoadingMore(false);
      }, 800);
    }
  };

  const [skipDeepSearch, setSkipDeepSearch] = useState(0)

  const handleDeepSearch = async (sortValue: number, filterValues: any) => {
    if (!searchQuery.trim()) {
      setIsSearching(false);
      return;
    }

    if (!sortValue && !filterValues) {
      resetFilters();
    }

    resetSelectionState();

    try {
      setIsLoadingMore(true);
      setIsSearching(true);
      setIsDeepSearchLoading(true);
      setDisplayedLectures([]); // Clear previous results to show skeleton

      const res = await fetchDeepSearchResults({
        searchQuery,
        playbackMode,
        sortBy: sortByOptions.find((opt: any) => opt.value === sortValue)
          ?.query,
        allFilter: filterValues ? filterValues : {},
      });

      setDisplayedLectures(res?.lectures);
      setHasMore(res?.hasMany);
      setSkipDeepSearch(res?.skip)
    } catch (error) {
      console.error("Error deep searching lectures:", error);
      setAllLectures([]);
      setDisplayedLectures([]);
      setHasMore(false);
    } finally {
      setIsLoadingMore(false);
        setIsDeepSearchLoading(false);
    }
  };

  const loadMoreDeepSearch = async () => {

    try {
      setIsSearching(true);

      const res = await fetchDeepSearchResults({
        searchQuery,
        playbackMode,
        sortBy: sortByOptions.find((opt: any) => opt.value === sortBy)
          ?.query,
        allFilter: selectedFilterValues,
        skip: skipDeepSearch
      });

      setDisplayedLectures((prev) => prev.concat(res?.lectures));
      setHasMore(res?.hasMany);
      setSkipDeepSearch(res?.skip)
    } catch (error) {
      message.error("Error loading more results.")
      console.error("Error loading more deep searching lectures:", error);
      setHasMore(false);
    }
  };



  const handleFilterChange = (values: any) => {
    resetSelectionState();

    if (!values || typeof values !== "object") {
      console.error("Invalid filter values:", values);
      return;
    }

    const hasActiveFilters = Object.values(values).some(
      (filterValues: any) =>
        Array.isArray(filterValues) && filterValues.length > 0
    );
    setIsFiltering(hasActiveFilters);
  };

  // Function to load more results for infinite scroll
  const loadMoreResults = useCallback(() => {
    if (!hasMore || isLoadingMore) return;

    setIsLoadingMore(true);

    setTimeout(() => {
      const nextPage = page + 1;
      const startIndex = (nextPage - 1) * resultsPerPage;
      const endIndex = startIndex + resultsPerPage;

      const nextResults = allLectures.slice(startIndex, endIndex);

      if (nextResults.length > 0) {
        setDisplayedLectures((prev) => [...prev, ...nextResults]);
        setPage(nextPage);
      }

      // Check if we've loaded all results
      if (endIndex >= allLectures.length) {
        setHasMore(false);
      }

      setIsLoadingMore(false);
    }, 500);
  }, [hasMore, isLoadingMore, page, allLectures, resultsPerPage]);

  // Function to handle lecture updates
  const handleLectureUpdated = async (
    updatedIds: (string | number)[],
    field: string,
    value: boolean
  ) => {
    if (updatedIds.length === 0) return;

    // Update all lectures
    const updatedAllLectures = await fetchAllMediaLibraryLectures();
    setAllLectures(updatedAllLectures);

    // Update library sections
    const updatedSections = { ...librarySections };
    Object.keys(updatedSections).forEach((sectionKey) => {
      if (sectionKey !== "recommendedPlaylists") {
        updatedSections[sectionKey] = updatedSections[sectionKey].map(
          (lecture: any) => {
            if (updatedIds.includes(lecture.id)) {
              return { ...lecture, [field]: value };
            }
            return lecture;
          }
        );
      }
    });
    setLibrarySections(updatedSections);

    // Update displayed lectures
    setDisplayedLectures((prevResults) =>
      prevResults.map((lecture: any) => {
        if (updatedIds.includes(lecture.id)) {
          return { ...lecture, [field]: value };
        }
        return lecture;
      })
    );

    setIsSelectFileOpen(false);
    setSelectedFiles([]);
  };

  // Render section helper function
  const renderSection = (
    title: string,
    data: any[],
    Component: React.FC<any>,
    key: string
  ) => {
    // If loading, show skeleton components
    if (libraryLoading) {
      return (
        <div className="flex flex-col gap-4" key={key}>
          <h1 className="text-[22px] min-[460px]:text-[24px] font-[600] leading-8">
            {title}
          </h1>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
            {Array(5)
              .fill(0)
              .map((_, index) => (
                <div key={`skeleton-${key}-${index}`}>
                  {key.includes("Playlists") ? (
                    <PlaylistCardSkeleton />
                  ) : (
                    <LectureCardSkeleton />
                  )}
                </div>
              ))}
          </div>
        </div>
      );
    }

    // If no data, return null
    if (!data || data.length === 0) return null;

    // If data is loaded, show actual components
    return (
      <div className="flex flex-col gap-4" key={key}>
        <h1 className="text-[22px] min-[460px]:text-[24px] font-[600] leading-8">
          {title}
        </h1>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
          {data.map((item: any) => (
            <div key={item.id}>
              <Component
                {...(key.includes("Playlists")
                  ? { playlistData: item }
                  : {
                      lectureData: item,
                      isSelectFileOpen,
                      selectedFiles,
                      setSelectedFiles,
                      onLectureUpdated: handleLectureUpdated,
                    })}
              />
            </div>
          ))}
        </div>
      </div>
    );
  };

  const sectionsConfig = [
    {
      title: "Latest uploads",
      key: "latestUploads",
      Component: LectureCard,
    },
    {
      title: "Popular",
      key: "popular",
      Component: LectureCard,
    },
    {
      title: "Latest in English",
      key: "latestEnglish",
      Component: LectureCard,
    },
    { title: "हिन्दी", key: "hindi", Component: LectureCard },
    { title: "বাংলা", key: "bangla", Component: LectureCard },
    { title: "Bhagavad-gītā", key: "bhgavadGita", Component: LectureCard },
    {
      title: "Śrīmad-Bhāgavatam",
      key: "shrimadBhagavatam",
      Component: LectureCard,
    },
    {
      title: "Śrī Caitanya-caritāmṛta",
      key: "caitanyaCaritamrta",
      Component: LectureCard,
    },
    { title: "Bhajans", key: "bhajans", Component: LectureCard },
    {
      title: "Viṣṇu-sahasranāma",
      key: "visnuSahasranama",
      Component: LectureCard,
    },
    {
      title: "Recommended Playlists",
      key: "recommendedPlaylists",
      Component: PlaylistCard,
    },
  ];

  return (
    <div className={`w-full h-full ${poppins.className} tracking-normal`}>
      <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b relative">
        {/* Result and selected count */}
        <div className="hidden md:flex gap-4 md:absolute top-5 min-[460px]:top-2 right-4">
          {isSelectFileOpen && (
            <h2
              className={`text-[12px] leading-5 font-[400] text-text-primary`}
            >
              <span className="font-[500]">Selected:</span>{" "}
              {selectedFiles.length}
            </h2>
          )}
          {(isSearching || isFiltering) && (
            <h2
              className={`text-[12px] leading-5 font-[400] text-text-primary `}
            >
              <span className="font-[500]">Result count:</span>{" "}
              {allLectures.length.toString()}
            </h2>
          )}
        </div>

        {/* Heading */}
        <h1 className="max-[392px]:text-[24px] max-[460px]:text-[24px] text-[28px] font-[600] leading-8">
          Media Library
        </h1>
        <div className="flex gap-2 items-end">
          {!isSelectFileOpen ? (
            <>
              <AudioToggleSwitch />
              {(isSearching || isFiltering) && <PlaybackMode />}
              {(isSearching || isFiltering) && <SortBy />}
              <Filter {...{ handleFilterChange }} />
              <SelectFiles {...{ setIsSelectFileOpen }} />
            </>
          ) : (
            <>
              <AddToPlaylist
                {...{ selectedFiles, setIsSelectFileOpen, setSelectedFiles }}
              />
              <AddToFavorites
                selectedFiles={selectedFiles}
                onFavoritesUpdated={handleLectureUpdated}
              />
              <RemoveFavorites
                selectedFiles={selectedFiles}
                onFavoritesUpdated={handleLectureUpdated}
              />
              <MarkAsComplete
                {...{
                  selectedFiles,
                  onCompleteUpdated: handleLectureUpdated,
                }}
              />
              <ResetAsComplete
                {...{
                  selectedFiles,
                  onCompleteUpdated: handleLectureUpdated,
                }}
              />
              <div
                className="h-[32px] flex gap-2 items-center pt-[2px] px-2.5 md:px-3 text-[13px] text-left bg-[#E0E0E0] rounded-[12px] hover:opacity-85 cursor-pointer transition-all"
                onClick={resetSelectionState}
              >
                <h2 className="text-[13px] leading-5 font-[400] text-text-primary">
                  <span className="sm:block hidden">Cancel</span>{" "}
                  <span className="block sm:hidden">
                    <CloseOutlined />
                  </span>
                </h2>
              </div>
            </>
          )}
        </div>
      </div>

      <div
        id="scrollableDiv"
        className="w-full h-[calc(100%-60px)] md:h-[calc(100%-84px)] p-5 pb-40 !overflow-x-hidden overflow-y-auto scrollbar"
      >
        {!isSearching && !isFiltering ? (
          <div className="flex flex-col gap-6">
            {sectionsConfig.map(({ title, key, Component }) =>
              renderSection(title, librarySections[key], Component, key)
            )}
          </div>
        ) : (
          <div className="w-full flex flex-col gap-4">
            <div className="flex flex-col gap-1">
              <h1 className="text-[22px] min-[460px]:text-[24px] font-[600] leading-8">
                {/* {isSearching
                                    ? `Search Results for "${searchQuery}"`
                                    : "Filtered Results"} */}
                {isSearching
                  ? deepSearch
                    ? `Deep search Results for "${searchQuery}"`
                    : `Search Results for "${searchQuery}"`
                  : "Filtered Results"}
              </h1>
              {(() => {
                // Check if any filters are applied
                const hasFilters = Object.values(selectedFilterValues).some(
                  (values: any) => Array.isArray(values) && values.length > 0
                );

                if (hasFilters) {
                  return (
                    <div className="flex flex-wrap items-center gap-2 mb-2">
                      <span className="text-sm text-gray-600">
                        Filters applied:
                      </span>
                      {Object.entries(selectedFilterValues).map(
                        ([filterType, values]: [string, any]) => {
                          if (Array.isArray(values) && values.length > 0) {
                            return (
                              <span
                                key={filterType}
                                className="text-[10px] bg-primary-light text-primary px-2 py-1 rounded-md"
                              >
                                {filterType}: {values.length}
                              </span>
                            );
                          }
                          return null;
                        }
                      )}
                    </div>
                  );
                }
                return null;
              })()}
              {sortBy !== 0 && (
                <div className="text-sm text-gray-600 mb-2">
                  Sorted by:{" "}
                  {sortByOptions.find((option: any) => option.value === sortBy)
                    ?.label || "Default view"}
                </div>
              )}
            </div>
            {isDeepSearchLoading ? (
              // Show skeleton loading during deep search
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                {Array(15)
                  .fill(0)
                  .map((_, index) => (
                    <LectureCardSkeleton
                      key={`deep-search-skeleton-${index}`}
                    />
                  ))}
              </div>
            ) : displayedLectures.length > 0 ? (
              <>
                {isDeepSearching ? (
                  <InfiniteScroll
                    dataLength={displayedLectures.length}
                    next={loadMoreDeepSearch}
                    hasMore={hasMore}
                    loader={
                      <div className="w-full grid grid-cols-5 gap-5 mt-4">
                        {Array(5)
                          .fill(0)
                          .map((_, index) => (
                            <LectureCardSkeleton
                              key={`search-skeleton-${index}`}
                            />
                          ))}
                      </div>
                    }
                    endMessage={null}
                    scrollableTarget="scrollableDiv"
                    scrollThreshold={0.95}
                    className="!overflow-x-hidden"
                  >
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                      {displayedLectures.map((lecture: any) => (
                        <LectureCard
                          key={lecture.id}
                          lectureData={lecture}
                          {...{
                            isSelectFileOpen,
                            selectedFiles,
                            setSelectedFiles,
                            onLectureUpdated: handleLectureUpdated,
                          }}
                        />
                      ))}
                    </div>
                  </InfiniteScroll>
                ) : (
                  <InfiniteScroll
                    dataLength={displayedLectures.length}
                    next={loadMoreResults}
                    hasMore={hasMore}
                    loader={
                      <div className="w-full grid grid-cols-5 gap-5 mt-4">
                        {Array(5)
                          .fill(0)
                          .map((_, index) => (
                            <LectureCardSkeleton
                              key={`search-skeleton-${index}`}
                            />
                          ))}
                      </div>
                    }
                    endMessage={null}
                    scrollableTarget="scrollableDiv"
                    scrollThreshold={0.95}
                    className="!overflow-x-hidden"
                  >
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                      {displayedLectures.map((lecture: any) => (
                        <LectureCard
                          key={lecture.id}
                          lectureData={lecture}
                          {...{
                            isSelectFileOpen,
                            selectedFiles,
                            setSelectedFiles,
                            onLectureUpdated: handleLectureUpdated,
                          }}
                        />
                      ))}
                    </div>
                  </InfiniteScroll>
                )}
              </>
            ) : (
              <div className="text-center py-10">
                <p className="text-lg text-gray-500">
                  {isSearching
                    ? `No results found for "${searchQuery}"${
                        isFiltering ? " with the applied filters" : ""
                      }`
                    : "No results match the applied filters"}
                </p>
                <p className="text-sm text-gray-400 mt-2">
                  {isSearching
                    ? "Try different keywords or check your spelling"
                    : "Try adjusting your filter criteria"}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MediaLibrary;
