import { Button, Input, Popover } from "antd";
import Image from "next/image";
import React, { useState, useMemo, useEffect } from "react";
import {
  filterOptions,
  // LANGUAGE,
  // COUNTRY,
  // PLACE,
  // YEAR,
  MONTH,
  // CATEGORY,
  // TRANSLATION,
  TYPE,
  LENGTH,
  COMPLETED,
  // TAG,
} from "@/src/libs/constant";
import { Poppins } from "next/font/google";
import Checkbox from "@mui/material/Checkbox";
import CheckboxSelected from "../ui/checkboxSelected";
import CheckboxUnselected from "../ui/checkboxUnselected";
import { useFilterContext } from "@/src/context/filter.context";
import { useSearchContext } from "@/src/context/search.context";
import { usePathname } from "next/navigation";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

interface FilterProps {
  onFilterChange?: (selectedFilters: { [key: string]: string[] }) => void;
  handleFilterChange?: (selectedFilters: { [key: string]: string[] }) => void;
}

const Filter = ({ onFilterChange, handleFilterChange }: FilterProps) => {
  const pathName = usePathname();
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState("LANGUAGE");
  const [searchTerm, setSearchTerm] = useState("");

  const { selectedFilterValues, setSelectedFilterValues, setIsFiltering } =
    useFilterContext();
  const { isDeepSearching } = useSearchContext();

  // Filter options based on deep search status
  // During deep search, only these filters are supported: LANGUAGE, YEAR, COUNTRY, PLACE, CATEGORY, MONTH
  const availableFilterOptions = useMemo(() => {
    if (isDeepSearching && pathName === "/media-library") {
      return filterOptions.filter((option) =>
        ["LANGUAGE", "YEAR", "COUNTRY", "PLACE", "CATEGORY", "MONTH"].includes(option.value)
      );
    }
    return filterOptions;
  }, [isDeepSearching, pathName]);

  // Reset selected filter if it's not available in current context
  useEffect(() => {
    const isCurrentFilterAvailable = availableFilterOptions.some(
      (option) => option.value === selectedFilter
    );
    if (!isCurrentFilterAvailable && availableFilterOptions.length > 0) {
      setSelectedFilter(availableFilterOptions[0].value);
    }
  }, [availableFilterOptions, selectedFilter]);

  // Track temporary selections before applying
  const [tempSelectedValues, setTempSelectedValues] = useState<{
    [key: string]: string[];
  }>({
    LANGUAGE: [],
    COUNTRY: [],
    PLACE: [],
    YEAR: [],
    MONTH: [],
    CATEGORY: [],
    TRANSLATION: [],
    TYPE: [],
    LENGTH: [],
    COMPLETED: [],
    TAG: [],
  });

  // Load localStorage filters into dynamic state
  const [dynamicFilters, setDynamicFilters] = useState<{
    LANGUAGE: any[];
    COUNTRY: any[];
    PLACE: any[];
    YEAR: any[];
    CATEGORY: any[];
    TRANSLATION: any[];
    TAG: any[];
  }>({
    LANGUAGE: [],
    COUNTRY: [],
    PLACE: [],
    YEAR: [],
    CATEGORY: [],
    TRANSLATION: [],
    TAG: [],
  });

  useEffect(() => {
    const loadFiltersFromLocalStorage = () => {
      try {
        setDynamicFilters({
          LANGUAGE: JSON.parse(localStorage.getItem("languages") || "[]"),
          COUNTRY: JSON.parse(localStorage.getItem("countries") || "[]"),
          PLACE: JSON.parse(localStorage.getItem("place") || "[]"),
          YEAR: JSON.parse(localStorage.getItem("years") || "[]"),
          CATEGORY: JSON.parse(localStorage.getItem("categories") || "[]"),
          TRANSLATION: JSON.parse(localStorage.getItem("translation") || "[]"),
          TAG: JSON.parse(localStorage.getItem("tags") || "[]"),
        });
      } catch (error) {
        console.error("Error loading filters from localStorage:", error);
      }
    };

    loadFiltersFromLocalStorage();
  }, []);

  // Calculate total number of selected filters
  const totalSelectedFilters: any = useMemo(() => {
    return Object.values(selectedFilterValues).reduce(
      (total, current: any) => total + current.length,
      0
    );
  }, [selectedFilterValues]);

  // Calculate if any temporary filters are selected
  const hasTempSelectedFilters: any = useMemo(() => {
    return Object.values(tempSelectedValues).some(
      (filterValues) => filterValues.length > 0
    );
  }, [tempSelectedValues]);

  // const handleCheckboxChange = (value: string) => {
  //   setTempSelectedValues((prev) => {
  //     const currentSelected = [...prev[selectedFilter]];
  //     if (currentSelected.includes(value)) {
  //       return {
  //         ...prev,
  //         [selectedFilter]: currentSelected.filter((item) => item !== value),
  //       };
  //     } else {
  //       return {
  //         ...prev,
  //         [selectedFilter]: [...currentSelected, value],
  //       };
  //     }
  //   });
  // };

  const handleCheckboxChange = (value: string) => {
    setTempSelectedValues((prev) => {
      const currentSelected = Array.isArray(prev[selectedFilter])
        ? [...prev[selectedFilter]]
        : [];

      if (currentSelected.includes(value)) {
        return {
          ...prev,
          [selectedFilter]: currentSelected.filter((item) => item !== value),
        };
      } else {
        return {
          ...prev,
          [selectedFilter]: [...currentSelected, value],
        };
      }
    });
  };

  const handleApply = () => {
    // Create a deep copy of the temporary values to avoid reference issues
    let newFilterValues = JSON.parse(JSON.stringify(tempSelectedValues));

    // If deep search is active, only keep filters that are supported
    if (isDeepSearching && pathName === "/media-library") {
      const supportedFilters = ["LANGUAGE", "YEAR", "COUNTRY", "PLACE", "CATEGORY", "MONTH"];
      const filteredValues: { [key: string]: string[] } = {};

      // Initialize all filter keys with empty arrays
      Object.keys(newFilterValues).forEach(key => {
        filteredValues[key] = [];
      });

      // Only keep values for supported filters
      supportedFilters.forEach(filterKey => {
        if (newFilterValues[filterKey]) {
          filteredValues[filterKey] = newFilterValues[filterKey];
        }
      });

      newFilterValues = filteredValues;
    }

    // Update the context state
    setSelectedFilterValues(newFilterValues);
    setSelectedFilter(availableFilterOptions[0]?.value || "LANGUAGE");
    setIsFilterOpen(false);

    // Call the handler function with the new filter values if provided
    if (handleFilterChange) {
      handleFilterChange(newFilterValues);
    }

    // Set filtering flag
    setIsFiltering(true);

    // Call the onFilterChange callback if provided
    if (onFilterChange) {
      onFilterChange(newFilterValues);
    }
  };

  const handleClearAll = () => {
    const emptyFilters = {
      LANGUAGE: [],
      COUNTRY: [],
      PLACE: [],
      YEAR: [],
      MONTH: [],
      CATEGORY: [],
      TRANSLATION: [],
      TYPE: [],
      LENGTH: [],
      COMPLETED: [],
      TAG: [],
    };

    // Update the context state
    setSelectedFilterValues(emptyFilters);
    setTempSelectedValues(emptyFilters);
    setSelectedFilter(availableFilterOptions[0]?.value || "LANGUAGE");
    setIsFilterOpen(false);

    // Call the handler function with empty filters if provided
    if (handleFilterChange) {
      handleFilterChange(emptyFilters);
    }

    // Reset filtering flag
    setIsFiltering(false);

    // Call the onFilterChange callback if provided
    if (onFilterChange) {
      onFilterChange(emptyFilters);
    }
  };

  // const getFilterData = (filterType: string) => {
  //   switch (filterType) {
  //     case "LANGUAGE":
  //       return dynamicFilters.LANGUAGE;
  //     case "COUNTRY":
  //       return dynamicFilters.COUNTRY;
  //     case "PLACE":
  //       return dynamicFilters.PLACE;
  //     case "YEAR":
  //       return dynamicFilters.YEAR;
  //     case "MONTH":
  //       return MONTH;
  //     case "CATEGORY":
  //       return dynamicFilters.CATEGORY;
  //     case "TRANSLATION":
  //       return dynamicFilters.TRANSLATION;
  //     case "TYPE":
  //       return TYPE;
  //     case "LENGTH":
  //       return LENGTH;
  //     case "COMPLETED":
  //       return COMPLETED;
  //     case "TAG":
  //       return dynamicFilters.TAG;
  //     default:
  //       return [];
  //   }

  // };

  const getFilterData = (filterType: string) => {
    let data: any[] = [];
    switch (filterType) {
      case "LANGUAGE":
        data = dynamicFilters.LANGUAGE;
        break;
      case "COUNTRY":
        data = dynamicFilters.COUNTRY;
        break;
      case "PLACE":
        data = dynamicFilters.PLACE;
        break;
      case "YEAR":
        // data = dynamicFilters.YEAR;
        // break;
        return dynamicFilters.YEAR;
      case "MONTH":
        // data = MONTH;
        // break;
        return MONTH;
      case "CATEGORY":
        data = dynamicFilters.CATEGORY;
        break;
      case "TRANSLATION":
        data = dynamicFilters.TRANSLATION;
        break;
      case "TYPE":
        data = TYPE;
        break;
      case "LENGTH":
        data = LENGTH;
        break;
      case "COMPLETED":
        data = COMPLETED;
        break;
      case "TAG":
        data = dynamicFilters.TAG;
        break;
      default:
        data = [];
        break;
    }
    // Sort by label (A-Z)
    return data.slice().sort((a, b) => a.label.localeCompare(b.label));
  };

  const handlePopoverOpen = (open: boolean) => {
    if (open) {
      // When opening, initialize temp selections with current selections
      setTempSelectedValues({ ...selectedFilterValues });
    }
    setIsFilterOpen(open);
  };

  const filterData = getFilterData(selectedFilter);
  const showSearch = filterData.length > 15;
  const filteredData = showSearch
    ? filterData.filter((item: any) =>
        item.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : filterData;

  const content = (
    <div
      className={`w-[280px] max-[440px]:w-[300px] min-[440px]:w-[360px] h-[340px] my-4 ${poppins.className}`}
    >
      <div className="w-full h-[280px] flex">
        <div className="w-[110px] max-[440px]:w-[120px] min-[440px]:w-[130px] border-r flex flex-col overflow-y-auto scrollbar max-h-[280px]">
          {availableFilterOptions.map((item: any) => (
            <div
              key={item.value}
              className={`w-full flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 sm:py-2.5 cursor-pointer transition-all duration-200 ${
                selectedFilter === item.value
                  ? "bg-zinc-200"
                  : "hover:bg-gray-100"
              }`}
              onClick={() => {
                setSelectedFilter(item.value);
                setSearchTerm("");
              }}
            >
              <h2 className="text-[11px] sm:text-[12px] min-[440px]:text-[13px] leading-5 font-[400] text-text-primary">
                {item.label}
                {tempSelectedValues[item.value]?.length > 0
                  ? ` (${tempSelectedValues[item.value].length})`
                  : ""}
              </h2>
            </div>
          ))}
        </div>

        {/* <div className="flex-1 overflow-y-auto scrollbar">
          <div className="flex flex-col gap-1">
            <div className="flex flex-col gap-1">
              {showSearch && (
                <Input
                  size="small"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="px-2 fixed"
                  style={{
                    boxShadow: "none",
                    borderRadius: 0,
                    borderLeft: 0,
                    borderRight: 0,
                    borderTop: 0,
                    borderBottom: "1px solid #d1d5db", // Tailwind's gray-300
                    background: "transparent",
                  }}
                  allowClear
                />
              )}
              {getFilterData(selectedFilter).map((item: any) => (
                <div
                  key={item.value}
                  className="flex items-center gap-1 px-2 cursor-pointer"
                >
                  <Checkbox
                    size="small"
                    icon={<CheckboxUnselected />}
                    checkedIcon={<CheckboxSelected />}
                    checked={tempSelectedValues[selectedFilter]?.includes(
                      item.value
                    )}
                    onChange={() => handleCheckboxChange(item.value)}
                    color="primary"
                  />
                  <label
                    className="text-[11px] sm:text-[12px] min-[440px]:text-[13px] leading-5 font-[400] text-textPrimary cursor-pointer"
                    onClick={() => handleCheckboxChange(item.value)}
                  >
                    {item.label}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div> */}

        <div className="flex-1 flex flex-col">
          {showSearch && (
            <Input
              size="small"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="mb-2 px-2 border-0 border-b border-gray-300 "
              style={{
                boxShadow: "none",
                borderBottom: "1px solid #d1d5db",
                background: "transparent",
              }}
              allowClear
            />
          )}
          <div className="flex-1 overflow-y-auto scrollbar">
            <div className="flex flex-col gap-1">
              {filteredData.map((item: any) => (
                <div
                  key={item.value}
                  className="flex items-center gap-1 px-2 cursor-pointer"
                >
                  <Checkbox
                    size="small"
                    icon={<CheckboxUnselected />}
                    checkedIcon={<CheckboxSelected />}
                    checked={tempSelectedValues[selectedFilter]?.includes(
                      item.value
                    )}
                    onChange={() => handleCheckboxChange(item.value)}
                    color="primary"
                  />
                  <label
                    className="text-[11px] sm:text-[12px] min-[440px]:text-[13px] leading-5 font-[400] text-textPrimary cursor-pointer"
                    onClick={() => handleCheckboxChange(item.value)}
                  >
                    {item.label}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-end gap-2 items-center my-3 sm:my-3.5 mx-3 sm:mx-4">
        <Button
          className="text-[13px] sm:text-[15px] leading-6 font-[500] !text-[#1B1F3BCC] !border-none cursor-pointer transition-all shadow-none disabled:!bg-white disabled:opacity-50"
          onClick={handleClearAll}
          // disabled={!hasTempSelectedFilters}
        >
          Clear all
        </Button>
        <Button
          className="text-[13px] sm:text-[15px] leading-6 font-[500] !text-primary !border-none cursor-pointer transition-all shadow-none disabled:!bg-white disabled:opacity-50"
          onClick={handleApply}
          disabled={!hasTempSelectedFilters}
        >
          Apply
        </Button>
      </div>
    </div>
  );
  const isMobile = typeof window !== "undefined" && window.innerWidth <= 440;

  return (
    <div className="flex flex-col gap-1">
      <Popover
        // placement="topRight"
        placement={isMobile ? "bottom" : "topRight"} 
        overlayStyle={
          isMobile
            ? { width: "94vw",  transform: "none", }
            : {}
        }
        title={
          <h2 className="text-[16px] sm:text-[18px] leading-5 font-[500] px-3 pt-4 mt-2 text-text-primary">
            Filter
          </h2>
        }
        content={content}
        arrow={false}
        trigger={["click"]}
        open={isFilterOpen}
        onOpenChange={handlePopoverOpen}
        autoAdjustOverflow
      >
        <Button
          className={`h-[30px] sm:h-[32px] flex gap-1 sm:gap-2 items-center pt-[2px] px-1 md:px-3 text-[12px] sm:text-[13px] text-left shadow-none max-[768px]:border-none md:!border border-[#E0E0E0] rounded-[10px] sm:rounded-[12px] hover:!border-primary cursor-pointer transition-all relative ${
            (isFilterOpen || totalSelectedFilters > 0) && "!border-primary"
          }`}
        >
          <Image
            src="/images/helperComponents/filter.svg"
            width={20}
            height={20}
            alt=""
            className={`w-[18px] h-[18px] sm:w-[20px] sm:h-[20px]`}
          />
          <h2 className="text-[12px] sm:text-[13px] leading-5 font-[400] text-text-primary md:block hidden">
            Filter
          </h2>
          {totalSelectedFilters > 0 && (
            <div className="absolute -top-2 -right-2 w-4 h-4 sm:w-5 sm:h-5 bg-primary text-white text-[9px] sm:text-[10px] font-bold rounded-full flex items-center justify-center">
              {totalSelectedFilters}
            </div>
          )}
        </Button>
      </Popover>
    </div>
  );
};

export default Filter;
