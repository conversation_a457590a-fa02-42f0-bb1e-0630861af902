import { getAllLectures } from "./indexedDB.service";
import { fetchLectures, FetchLecturesPayload, getDeepSearchResults } from "../api/mediaLibrary.api";
import { fetchPlaylist } from "../api/playlist.api";
import { applyFilters, applySorting } from "../utils/helperFunctions";
import { mediaLibrarySections } from "../libs/constant";
import { getPopularLectureIds } from "../api/popular.api";

export interface FetchLibraryParams {
  playbackMode?: number;
}

export interface FetchMediaLibrarySearchParams {
  searchQuery?: string;
  sortBy?: number;
  allFilter?: { [key: string]: string[] };
  playbackMode?: number;
}

export interface FetchDeepSearchMediaLibraryParams {
  searchQuery: string;
  playbackMode?: number;
  sortBy?: string;
  allFilter?: { [key: string]: string[] };
  skip?: number;
}

// Helper function to apply search filtering
const applySearchFilter = (lectures: any[], searchQuery: string) => {
  if (!searchQuery.trim()) return lectures;

  // Convert search query to lowercase for case-insensitive comparison
  const query = searchQuery.toLowerCase().trim();

  // Split the query into words for full name search
  const queryWords = query
    .split(/\s+/)
    .filter((word: string) => word.length > 0);

  // Filter lectures by comparing search query with title array elements
  return lectures.filter((lecture: any) => {
    // Check if title exists and is an array
    if (!lecture.title || !Array.isArray(lecture.title)) return false;

    // Check for exact match first (highest priority)
    if (
      lecture.title.some(
        (titlePart: string) => titlePart.toLowerCase() === query
      )
    ) {
      return true;
    }

    // Check if any element in the title array contains the full search query
    if (
      lecture.title.some((titlePart: string) =>
        titlePart.toLowerCase().includes(query)
      )
    ) {
      return true;
    }

    // If the query has multiple words, check if all words appear in any title part
    if (queryWords.length > 1) {
      // Join all title parts into a single string for multi-word search
      const fullTitle = lecture.title.join(" ").toLowerCase();

      // Check if all query words are present in the full title
      const allWordsPresent = queryWords.every((word: string) =>
        fullTitle.includes(word)
      );

      if (allWordsPresent) {
        return true;
      }
    }

    return false;
  });
};

/**
 * Fetch and organize library sections data
 * @param params - Object containing playback mode for filtering
 * @returns Promise<any> - Object containing all library sections
 */
export const fetchLibrarySections = async (params: FetchLibraryParams = {}): Promise<any> => {
  const { playbackMode = 1 } = params;

  try {
    // Fetch all sections in parallel
    const promises = mediaLibrarySections.map(async (section) => {
      try {
        if (section.key === "popular") {
          try {
            const res = await getPopularLectureIds("thisMonth")

            const allLectures = await getAllLectures();

            const popularLectures = res.thisMonth.map((poplecture: any) =>
              allLectures.find((lecture: any) => lecture.id === poplecture?.playId)
            )
              .filter((lecture: any) => lecture !== undefined).slice(0, 5);

            return { key: section.key, data: popularLectures };
          } catch (error) {
            console.error("Error fetching popular lectures:", error);
            return { key: section.key, data: [] };
          }
        }
        const lectures = await fetchLectures(
          section.payload as FetchLecturesPayload
        );
        return { key: section.key, data: lectures };
      } catch (error) {
        console.error(`Error fetching ${section.key}:`, error);
        return { key: section.key, data: [] };
      }
    });

    // Fetch playlists
    const playlistsPromise = fetchPlaylist({
      type: "PublicPlaylists",
      limit: 5,
      orderBy: "creationTime",
      order: "desc",
    });

    const results = await Promise.all([...promises, playlistsPromise]);

    // Organize results into sections object
    const librarySections: any = {
      latestUploads: [],
      popular: [],
      latestEnglish: [],
      hindi: [],
      bangla: [],
      bhgavadGita: [],
      shrimadBhagavatam: [],
      caitanyaCaritamrta: [],
      bhajans: [],
      visnuSahasranama: [],
      recommendedPlaylists: [],
    };

    results.forEach((result: any, index) => {
      if (index === results.length - 1) {
        librarySections.recommendedPlaylists = result;
      } else {
        librarySections[result.key] = result.data;
      }
    });

    // Apply playback mode filtering to each section if needed
    if (playbackMode === 2) {
      Object.keys(librarySections).forEach((sectionKey) => {
        if (sectionKey !== "recommendedPlaylists") {
          librarySections[sectionKey] = librarySections[sectionKey].filter((lecture: any) => {
            return (
              lecture.resources &&
              lecture.resources.videos &&
              Array.isArray(lecture.resources.videos) &&
              lecture.resources.videos.length > 0
            );
          });
        }
      });
    }

    console.log("Fetched library sections successfully");
    return librarySections;

  } catch (error) {
    console.error("Error in fetchLibrarySections:", error);
    throw error;
  }
};

/**
 * Fetch and process media library search results based on provided parameters
 * @param params - Object containing search query, sort option, and filter values
 * @returns Promise<any[]> - Array of processed lectures
 */
export const fetchMediaLibrarySearch = async (params: FetchMediaLibrarySearchParams = {}): Promise<any[]> => {
  const {
    searchQuery = "",
    sortBy = 1,
    allFilter = {},
    playbackMode = 1
  } = params;

  try {
    // Get all lectures from IndexedDB
    const allLectures = await getAllLectures();

    let searchResults = allLectures;

    // Apply search filtering if search query is provided
    if (searchQuery.trim()) {
      searchResults = applySearchFilter(allLectures, searchQuery);
    }

    // Apply filters (including playback mode)
    const hasActiveFilters = Object.values(allFilter).some(
      (filterValues: any) => Array.isArray(filterValues) && filterValues.length > 0
    );

    if (hasActiveFilters || playbackMode !== 1) {
      searchResults = applyFilters(searchResults, allFilter, playbackMode);
    }

    // Apply sorting
    if (sortBy !== 1) {
      searchResults = applySorting(searchResults, sortBy);
    }

    console.log(`Processed ${searchResults.length} media library search results`);
    return searchResults;

  } catch (error) {
    console.error("Error in fetchMediaLibrarySearch:", error);
    throw error;
  }
};

/**
 * Fetch and process deep search results for media library based on provided parameters
 * @param params - Object containing search query, playback mode, sort option, and filter values
 * @returns Promise<any[]> - Array of processed lectures from deep search
 */
export const fetchDeepSearchResults = async (params: FetchDeepSearchMediaLibraryParams): Promise<any> => {
  const {
    searchQuery,
    sortBy = "",
    allFilter = {},
    skip = 0
  } = params;

  if (!searchQuery.trim()) {
    return [];
  }

  console.log('allFilter', allFilter)

  try {
    // Base search params
    const searchParams: Record<string, string | number> = {
      size: 20,
      from: skip,
      query: searchQuery,
      sort: sortBy,
    };

    const filterMap: Record<string, string> = {
      language: "LANGUAGE",
      year: "YEAR",
      country: "COUNTRY",
      place: "PLACE",
      category: "CATEGORY",
      month: "MONTH",
    };

    // Add filters if they have values
    for (const [queryKey, filterKey] of Object.entries(filterMap)) {
      const values = allFilter[filterKey];
      if (Array.isArray(values) && values.length > 0) {
        // ✅ Join all selected values with comma
        searchParams[queryKey] = values.join(",");
      }
    }

    // Get all lectures from IndexedDB and perform deep search (first page)
    const [allLectures, response] = await Promise.all([
      getAllLectures(),
      getDeepSearchResults(searchParams)
    ]);

    if (!response?.data?.transcriptions) {
      return [];
    }

    let allTranscriptions = response.data.transcriptions || [];
    const total = response.data.total;

    // Build a map of id -> count from the deep search response
    const countMap = new Map<number, number>();
    allTranscriptions.forEach((lecture: any) => {
      if (lecture.id !== undefined && lecture.count !== undefined) {
        countMap.set(lecture.id, lecture.count);
      }
    });

    // Extract lecture IDs from deep search response
    const responseIds = allTranscriptions.map((lecture: any) => lecture.id) || [];

    // Get matching lectures from IndexedDB and attach count
    let deepSearchResults = responseIds
      .map((id: any) => {
        const lecture = allLectures.find((lecture: any) => lecture.id === id);
        if (lecture) {
          return { ...lecture, count: countMap.get(id) ?? 0 };
        }
        return undefined;
      })
      .filter((lecture: any) => lecture !== undefined);

    return {
      lectures: deepSearchResults,
      hasMany: deepSearchResults.length + skip < total,
      skip: skip + deepSearchResults.length
    };

  } catch (error) {
    console.error("Error in fetchDeepSearchMediaLibrary:", error);
    throw error;
  }
};

// export const fetchDeepSearchMediaLibrary = async (params: FetchDeepSearchMediaLibraryParams): Promise<any[]> => {
//   const {
//     searchQuery,
//     playbackMode = 1,
//     sortBy = 0,
//     allFilter = {}
//   } = params;

//   if (!searchQuery.trim()) {
//     return [];
//   }

//   try {
//     // Prepare deep search parameters
//     const searchParams = {
//       size: 20,
//       from: 0,
//       query: searchQuery,
//       sort: sortBy.toString()
//     };

//     // Get all lectures from IndexedDB and perform deep search (first page)
//     const [allLectures, response] = await Promise.all([
//       getAllLectures(),
//       getDeepSearchResults(searchParams)
//     ]);

//     if (!response?.data?.transcriptions) {
//       return [];
//     }

//     let allTranscriptions = response.data.transcriptions || [];
//     const total = response.data.total;
//     // let fetched = allTranscriptions.length;
//     // let from = fetched;

//     // If more than 9000, fetch remaining in batches
//     // while (fetched < total) {
//     //   const nextBatch = await getDeepSearchResults({
//     //     query: searchQuery,
//     //     size: 9000,
//     //     from: from,
//     //   });
//     //   if (nextBatch?.data?.transcriptions?.length) {
//     //     allTranscriptions = allTranscriptions.concat(nextBatch.data.transcriptions);
//     //     fetched += nextBatch.data.transcriptions.length;
//     //     from += nextBatch.data.transcriptions.length;
//     //   } else {
//     //     break;
//     //   }
//     // }

//     // Build a map of id -> count from the deep search response
//     const countMap = new Map<number, number>();
//     allTranscriptions.forEach((lecture: any) => {
//       if (lecture.id !== undefined && lecture.count !== undefined) {
//         countMap.set(lecture.id, lecture.count);
//       }
//     });

//     // Extract lecture IDs from deep search response
//     const responseIds = allTranscriptions.map((lecture: any) => lecture.id) || [];

//     // Get matching lectures from IndexedDB and attach count
//     let deepSearchResults = responseIds
//       .map((id: any) => {
//         const lecture = allLectures.find((lecture: any) => lecture.id === id);
//         if (lecture) {
//           return { ...lecture, count: countMap.get(id) ?? 0 };
//         }
//         return undefined;
//       })
//       .filter((lecture: any) => lecture !== undefined);

//     // Apply filters (including playback mode)
//     // const hasActiveFilters = Object.values(allFilter).some(
//     //   (filterValues: any) => Array.isArray(filterValues) && filterValues.length > 0
//     // );

//     // if (hasActiveFilters || playbackMode !== 1) {
//     //   deepSearchResults = applyFilters(deepSearchResults, allFilter, playbackMode);
//     // }

//     // // Apply sorting
//     // if (sortBy !== 1) {
//     //   deepSearchResults = applySorting(deepSearchResults, sortBy);
//     // }

//     console.log(`Processed ${deepSearchResults.length} deep search media library results`);
//     return deepSearchResults;

//   } catch (error) {
//     console.error("Error in fetchDeepSearchMediaLibrary:", error);
//     throw error;
//   }
// };

/**
 * Get all lectures for filtering and searching
 * @returns Promise<any[]> - Array of all lectures
 */
export const fetchAllMediaLibraryLectures = async (): Promise<any[]> => {
  try {
    const lectures = await getAllLectures();
    console.log(`Fetched ${lectures.length} lectures for media library`);
    return lectures;
  } catch (error) {
    console.error("Error in fetchAllMediaLibraryLectures:", error);
    throw error;
  }
};
