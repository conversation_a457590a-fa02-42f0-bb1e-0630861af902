import { openDB, IDBPDatabase } from 'idb';

// Define the database name and version
const DB_NAME = 'bvksMediaLibrary';
const DB_VERSION = 2; // Incremented to trigger schema update

// Define the object store names
const LECTURES_STORE = 'lectures';
const SYNC_INFO_STORE = 'syncInfo';

// Interface for lecture data
export interface Lecture {
  id: string;
  title: string[];
  thumbnailUrl?: string;
  thumbnail?: string;
  audioUrl: string;
  category: string[];
  language: {
    main: string;
    [key: string]: string;
  };
  dateOfRecording: any;
  length: number;
  creationTimestamp: number;
  // Fields from lectureInfo
  completed?: boolean;
  isCompleted?: boolean;
  documentId?: string;
  documentPath?: string;
  downloadPlace?: number;
  downloaded?: boolean;
  favourite?: boolean;
  isFavourite?: boolean;
  favouritePlace?: number;
  inPrivateList?: boolean;
  inPublicList?: boolean;
  lastModifiedTimestamp?: number;
  lastPlayedPoint?: number; // Time in seconds where user last stopped listening
  totalPlayedNo?: number;
  totalPlayedTime?: number;
  totallength?: number;
  [key: string]: any;
}

// Interface for sync information
export interface SyncInfo {
  id: string;
  lastSyncTimestamp: number;
  totalLectures: number;
}

// Initialize the database
export const initDB = async (): Promise<IDBPDatabase> => {
  try {
    const db = await openDB(DB_NAME, DB_VERSION, {
      upgrade(db) {
        // Create lectures store if it doesn't exist
        if (!db.objectStoreNames.contains(LECTURES_STORE)) {
          // Simple object store with id as key, no indexes needed since we're doing in-memory filtering
          db.createObjectStore(LECTURES_STORE, { keyPath: 'id' });
        }

        // Create sync info store if it doesn't exist
        if (!db.objectStoreNames.contains(SYNC_INFO_STORE)) {
          db.createObjectStore(SYNC_INFO_STORE, { keyPath: 'id' });
        }
      },
    });
    return db;
  } catch (error) {
    console.error('Error initializing IndexedDB:', error);
    throw error;
  }
};

// Store lectures in IndexedDB
export const storeLectures = async (lectures: Lecture[]): Promise<void> => {
  try {
    const db = await initDB();
    const tx = db.transaction(LECTURES_STORE, 'readwrite');
    const store = tx.objectStore(LECTURES_STORE);

    // Use Promise.all for better performance with large datasets
    const promises = lectures.map(lecture => store.put(lecture));
    await Promise.all(promises);

    await tx.done;
    console.log(`Successfully stored ${lectures.length} lectures in IndexedDB`);
  } catch (error) {
    console.error('Error storing lectures in IndexedDB:', error);
    throw error;
  }
};

// Get all lectures from IndexedDB
export const getAllLectures = async (): Promise<Lecture[]> => {
  try {
    const db = await initDB();
    return db.getAll(LECTURES_STORE);
  } catch (error) {
    console.error('Error getting all lectures from IndexedDB:', error);
    throw error;
  }
};

// This function is no longer needed as we're doing all filtering in mediaLibrary.api.ts
// Keeping it for backward compatibility but just returning all lectures
export const getLectures = async (): Promise<Lecture[]> => {
  try {
    // Simply return all lectures - filtering and sorting is done in the API layer
    return getAllLectures();
  } catch (error) {
    console.error('Error getting lectures from IndexedDB:', error);
    throw error;
  }
};

// Update sync information
export const updateSyncInfo = async (syncInfo: SyncInfo): Promise<void> => {
  try {
    const db = await initDB();
    const tx = db.transaction(SYNC_INFO_STORE, 'readwrite');
    await tx.store.put(syncInfo);
    await tx.done;
  } catch (error) {
    console.error('Error updating sync info in IndexedDB:', error);
    throw error;
  }
};

// Get sync information
export const getSyncInfo = async (): Promise<SyncInfo | undefined> => {
  try {
    const db = await initDB();
    return db.get(SYNC_INFO_STORE, 'syncInfo');
  } catch (error) {
    console.error('Error getting sync info from IndexedDB:', error);
    throw error;
  }
};

// Clear all data from the database
export const clearDatabase = async (): Promise<void> => {
  try {
    const db = await initDB();
    const tx = db.transaction(LECTURES_STORE, 'readwrite');
    await tx.store.clear();
    await tx.done;
    console.log('IndexedDB lectures store cleared successfully');
  } catch (error) {
    console.error('Error clearing IndexedDB:', error);
    throw error;
  }
};

// Delete the entire IndexedDB database
export const deleteDatabase = async (): Promise<void> => {
  try {
    // Use the native indexedDB API to delete the database
    const deleteRequest = window.indexedDB.deleteDatabase(DB_NAME);

    return new Promise((resolve, reject) => {
      deleteRequest.onerror = () => {
        console.error('Error deleting IndexedDB database');
        reject(new Error('Failed to delete IndexedDB database'));
      };

      deleteRequest.onsuccess = () => {
        console.log('IndexedDB database deleted successfully');
        resolve();
      };
    });
  } catch (error) {
    console.error('Error deleting IndexedDB:', error);
    throw error;
  }
};

// Get the count of lectures in IndexedDB
export const getLectureCount = async (): Promise<number> => {
  try {
    const db = await initDB();
    const tx = db.transaction(LECTURES_STORE, 'readonly');
    const count = await tx.store.count();
    return count;
  } catch (error) {
    console.error('Error getting lecture count from IndexedDB:', error);
    throw error;
  }
};

// Get a single lecture by ID from IndexedDB
export const getLectureById = async (id: string | number): Promise<Lecture | undefined> => {
  try {
    const db = await initDB();
    return db.get(LECTURES_STORE, id);
  } catch (error) {
    console.error(`Error getting lecture with ID ${id} from IndexedDB:`, error);
    throw error;
  }
};

// Update a single lecture in IndexedDB
export const updateLecture = async (lecture: Lecture): Promise<void> => {
  try {
    const db = await initDB();
    const tx = db.transaction(LECTURES_STORE, 'readwrite');
    await tx.store.put(lecture);
    await tx.done;
    // console.log(`Successfully updated lecture with ID ${lecture.id} in IndexedDB`);
  } catch (error) {
    console.error(`Error updating lecture with ID ${lecture.id} in IndexedDB:`, error);
    throw error;
  }
};
